<!DOCTYPE html><html lang=en><head><script>window.Vise={}</script> <meta charset=UTF-8> <meta http-equiv=X-UA-Compatible content="IE=edge"> <meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1"> <link rel=preconnect href=https://isee.weishi.qq.com/ > <link rel=preconnect href=https://isee.weishi.qq.com/ crossorigin=anonymous> <link rel=preconnect href=https://api.weishi.qq.com/ crossorigin=anonymous> <link rel=dns-prefetch href=https://puui.qpic.cn/ > <link rel=dns-prefetch href=https://isee.weishi.qq.com/ > <link rel=dns-prefetch href=https://api.weishi.qq.com/ > <link rel=preload href=https://isee.weishi.qq.com/vise-public/aegis/2201280830/aegis.min.js as=script crossorigin=anonymous> <link rel=preload href=https://res.ab.qq.com/tab/abt_jssdk.min.js as=script> <!-- 设置默认背景色 --> <style>html{background:#fff}</style> <!-- 提供一个放置自定义数据的全局变量 --> <script>window.Vise=window.Vise||{},window.Vise.userData={},window.Vise.userUtils={}</script> <!-- 用来放置半SSR数据的插桩 --> <!--csr-home-data--> <!-- 提供工具函数 --> <script>window.Vise||(window.Vise={}),window.Vise.userUtils||(window.Vise.userUtils={}),window.Vise.userUtils.parseUrl=function(e){if("string"!=typeof e)return null;var n=e.split("#"),t=n[0];return{hash:n[1],params:(t.split("?")[1]||"").split("&").reduce((function(e,n){var t=e;if(!n)return t;var i="",[r,o]=n.split("=");if(!r)return t;var s=decodeURIComponent(r);return i=o?decodeURIComponent(o):o,t[s]=i,t}),{})}},window.Vise.userUtils.loadScript=function(e,n){var t=document.createElement("script"),i=document.head||document.getElementsByTagName("head")[0];t.src=e,t.addEventListener("load",(function(){"function"==typeof n&&n()})),t.addEventListener("error",(function(){i.removeChild(t)})),i.appendChild(t)},window.Vise.userUtils.getCookie=function(e,n){var t="string"!=typeof n?document.cookie:n,i=new RegExp("(?:^|;\\s*)"+e+"=([^;]*)"),r=t.match(i);return r&&r[1]||""}</script> <!-- 提供debug工具，url上增加query， erudaDebug=1 --> <script>!function(){window.Vise||(window.Vise={});var i=window.Vise.userUtils&&window.Vise.userUtils.parseUrl,e=window.Vise.userUtils&&window.Vise.userUtils.loadScript;if(i&&e){var s=i(window.location.href),n=s&&s.params||{};if(Boolean(Number(n.erudaDebug))){e("https://qzonestyle.gtimg.cn/qzone/qzact/act/external/weishi/ugactivity/eruda.js",(function(){window.eruda.init()}))}}}()</script> <!-- 订阅 PerformanceObserver 的绘制性能测速 --> <script>!function(e,r){try{PerformanceObserver||console.warn("[subscribePerformancePaint] 不支持 window.PerformanceObserver"),new PerformanceObserver((function(r){var n=r.getEntries().reduce((function(e,r){return e[r.name]=r.toJSON(),e}),{});e.Vise||(e.Vise={}),e.Vise.userData||(e.Vise.userData={}),e.Vise.userData.perfMetrics=n})).observe({entryTypes:["paint"]})}catch(e){console.error("[subscribePerformancePaint] 执行异常，err: ",e)}}(window,document)</script> <!-- 提供全局通用样式 --> <script>!function(){window.Vise||(window.Vise={});var e=window.Vise.userUtils&&window.Vise.userUtils.parseUrl;if(e){var t=e(window.location.href),a=t&&t.params||{},i=Boolean(navigator.userAgent.match("_WEISHI_")),n=parseInt(a.titleh,10)||48,d=parseInt(a.statush,10)||2;window.Vise.userData||(window.Vise.userData={}),window.Vise.userData.navBar={isInWeishiApp:i,titleh:n,statush:d};var o,p,r,h,s=document.createElement("style"),l=".global__nav-bar { padding-top: ${paddingTop} !important; height: ${height} !important; }".replace("${paddingTop}",String(i?d:0)+"px").replace("${height}",String(i?n:50)+"px"),c=".global__video-store-selector { height: ${height} !important; }".replace("${height}","calc(100vh - "+(i?d+n:50)+"px)"),g=".global__home-tab { padding-top: ${paddingTop} !important; }".replace("${paddingTop}",String(i?d:0)+"px"),m=".global__home-tab-fixedhelper { height: ${height} !important; }".replace("${height}",`calc(1.28rem + ${String(i?d:0)+"px"})`);s.setAttribute("type","text/css"),s.appendChild(document.createTextNode(l)),s.appendChild(document.createTextNode(c)),o=navigator.userAgent,p=o.match(/(iPad).*OS\s([\d_]+)/),r=!p&&o.match(/(iPhone).*OS\s([\d_]+)/),h=o.match(/(iPod).*OS\s([\d_]+)/),p||r||h||(s.appendChild(document.createTextNode(g)),s.appendChild(document.createTextNode(m))),(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}}()</script> <link rel=icon href=https://i.gtimg.cn/qz-proj/weishi-pc/img/favicon.ico><!--START_TITLE--><title>腾讯微视</title><!--END_TITLE--><link rel="modulepreload" crossorigin href="https://isee.weishi.qq.com/vise/share/assets/index.c2b4d6f3.js"><link rel="stylesheet" href="https://isee.weishi.qq.com/vise/share/assets/index.1b376b46.css"><link rel="modulepreload" crossorigin href="https://isee.weishi.qq.com/vise/share/assets/index_old.30d9b847.js"><link rel="stylesheet" href="https://isee.weishi.qq.com/vise/share/assets/index_old.ed267959.css"> <script type=module crossorigin src=https://isee.weishi.qq.com/vise/share/assets/index.5228f909.js></script> <link rel=stylesheet href=https://isee.weishi.qq.com/vise/share/assets/index.8935d24b.css> <script type=module>try{import("_").catch((()=>1))}catch(t){}window.__vite_is_dynamic_import_support=!0</script> <script type=module>!function(){if(!window.__vite_is_dynamic_import_support){console.warn("vite: loading legacy build because dynamic import is unsupported, syntax error above should be ignored");var e=document.getElementById("vite-legacy-polyfill"),t=document.createElement("script");t.src=e.src,t.onload=function(){System.import(document.getElementById("vite-legacy-entry").getAttribute("data-src"))},document.body.appendChild(t)}}()</script> <script>!function(e){function t(e,o){return!(!e||e===document.documentElement)&&(-1!==o.indexOf(e)||t(e.parentElement,o))}function o(e){var t=0,r=0;if(e&&1===e.nodeType){t+=1;var n=e.children;if(n&&n.length)for(;r<n.length;r++)t+=o(n[r])}return t}e.userData||(e.userData={}),function(){if(MutationObserver){var r=["script","style","link","br"],n=[],a={},s=new MutationObserver((function(e){var s={roots:[],rootsDomNum:[],time:performance.now()};e.forEach((function(e){e&&e.addedNodes&&e.addedNodes.forEach&&e.addedNodes.forEach((function(e){1===e.nodeType&&(e.hasAttribute("AEGIS-FIRST-SCREEN-TIMING")||e.querySelector("[AEGIS-FIRST-SCREEN-TIMING]"))?(Object.prototype.hasOwnProperty.apply(a,[s.time])||(a[s.time]=[]),a[s.time].push(e)):1!==e.nodeType||-1!==r.indexOf(e.nodeName.toLocaleLowerCase())||t(e,s.roots)||e.hasAttribute("AEGIS-IGNORE-FIRST-SCREEN-TIMING")||(s.roots.push(e),s.rootsDomNum.push(o(e)||0))}))})),s.roots.length&&n.push(s)}));s.observe(document,{childList:!0,subtree:!0}),e.userData.aegisPerfMaterial={markDoms:a,changeList:n,observeDom:s}}}()}(window.Vise)</script> <!--comment-head-end-inserted--></head> <body> <div id=app><div class="app-container"><!--[--><!--[--><div class="view-index"><div data-desc="for-aegis-fmp" AEGIS-FIRST-SCREEN-TIMING></div><div class="banner" style="" data-v-4ac84944><div class="info" data-v-4ac84944><!----><div data-v-4ac84944><i class="avatar" style="background-image:url(//avatar4.weishi.qq.com/ZXobSlt28-31PgraZ4N4O0q4VJI4aR3skIxzv_640.jpg);" data-v-4ac84944></i></div><!--[--><div class="detail" data-v-4ac84944><div class="person" data-v-4ac84944><div class="nick" data-v-4ac84944>     土蚝</div><div class="banner-txt" data-v-4ac84944>“发现了一条视频，邀你同看”</div></div></div><!--]--></div><div class="btn" data-v-4ac84944>打开看看 <!----></div></div><div class="swiper-container"><div class="swiper-wrapper"><!--[--><!--]--></div></div><!----><div class="rec-feeds" style="display:none;" data-v-09ec637b><div class="content" data-v-09ec637b><div class="btns" data-v-09ec637b><div class="replay" data-v-09ec637b><i data-v-09ec637b></i><span data-v-09ec637b>再看一次</span></div><div class="open-app" data-v-09ec637b><i data-v-09ec637b></i><span data-v-09ec637b>打开微视</span><!----></div></div><div class="title" data-v-09ec637b>热门推荐</div><ul class="list" data-v-09ec637b><!--[--><!--]--></ul><div class="swipper-for-bottom" data-v-09ec637b><!----><div class="loadingText" data-v-09ec637b> 正在努力加载 </div></div></div><!----></div><!----></div><div class="qrcode-wrap"><img class="qrcode" src="https://qzonestyle.gtimg.cn/qz-proj/weishi-pc/img/qrcode.png"><p class="txt"> 下载客户端 </p></div><!--]--><div class="qrcode-wrap"><img class="qrcode" src="https://qzonestyle.gtimg.cn/qz-proj/weishi-pc/img/qrcode.png"><p class="txt"> 下载客户端 </p></div><!--]--></div></div><script>try { window.Vise.initState = {"collectionInfo":null,"feedsList":[],"isCollection":false,"isDrama":false,"showCollection":false,"activeIndex":0,"shareUi":null,"activePlayerPlaying":false,"isNewUI":false,"zzConfig":null,"isInstall":null,"growthTestData":{"collectionPullNew":false,"collectionPullOld":true,"collectionStyle":1,"dataSource":0,"ext":{},"jumpMiniPlayer":0,"jumpNewPage":true,"moduleDataSrc":0,"modulePullNew":false,"modulePullOld":false,"msg":"succ","playBtnPullNew":false,"playBtnPullOld":true,"ret":0,"showModule":false,"strategyID":"0"},"banner":{"person":{"id":"1755431320152719","type":2,"uid":"77C0AD4692DBB01C412C60D3B3DAB42A","nick":"     土蚝","avatar":"https:\u002F\u002Favatar4.weishi.qq.com\u002FZXobSlt28-31PgraZ4N4O0q4VJI4aR3skIxzv_640.jpg"}},"errorMsg":"","isGrowthPopup":"1","dramaList":[],"showHandTip":false,"isFullScreen":false,"isNewPage":false}; } catch (err) { console.error('[Vise] fail to read initState.'); }</script> <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}()</script> <script nomodule id=vite-legacy-polyfill src=https://isee.weishi.qq.com/vise/share/assets/polyfills-legacy.7c3ebc7e.js></script> <script nomodule id=vite-legacy-entry data-src=https://isee.weishi.qq.com/vise/share/assets/index-legacy.d14c5455.js>System.import(document.getElementById("vite-legacy-entry").getAttribute("data-src"))</script> </body></html>