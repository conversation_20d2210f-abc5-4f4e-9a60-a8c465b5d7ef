#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频解析器 - 专业版本
使用专业的反爬虫技术、正确的解压缩和多种解析方法
"""

import requests
import re
import json
import time
import random
import gzip
import brotli
import base64
import hashlib
from urllib.parse import urlparse, parse_qs, unquote, quote
from fake_useragent import UserAgent
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DouyinProfessionalSpider:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="139", "Google Chrome";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)

    def get_real_url(self, short_url):
        """获取短链接的真实URL"""
        try:
            response = self.session.head(short_url, allow_redirects=True, timeout=15)
            real_url = response.url
            logger.info(f"真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            return short_url

    def extract_video_id(self, url):
        """从URL中提取视频ID"""
        patterns = [
            r'/video/(\d+)',
            r'modal_id=(\d+)',
            r'aweme_id=(\d+)',
            r'/share/video/(\d+)',
            r'/share/jx-video/(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                video_id = match.group(1)
                logger.info(f"提取到视频ID: {video_id}")
                return video_id
        
        logger.warning("未能提取到视频ID")
        return None

    def get_page_content_with_proper_decompression(self, url):
        """获取页面内容并正确解压缩"""
        try:
            headers = self.headers.copy()
            headers['Referer'] = 'https://www.douyin.com/'
            
            response = self.session.get(url, headers=headers, timeout=15, stream=True)
            
            if response.status_code == 200:
                # 获取原始内容
                raw_content = response.content
                
                # 检查内容编码
                content_encoding = response.headers.get('content-encoding', '').lower()
                logger.info(f"内容编码: {content_encoding}, 原始长度: {len(raw_content)}")
                
                try:
                    if content_encoding == 'gzip':
                        content = gzip.decompress(raw_content).decode('utf-8')
                        logger.info(f"成功解压缩gzip内容，解压后长度: {len(content)}")
                    elif content_encoding == 'br':
                        content = brotli.decompress(raw_content).decode('utf-8')
                        logger.info(f"成功解压缩brotli内容，解压后长度: {len(content)}")
                    elif content_encoding == 'deflate':
                        import zlib
                        content = zlib.decompress(raw_content).decode('utf-8')
                        logger.info(f"成功解压缩deflate内容，解压后长度: {len(content)}")
                    else:
                        # 尝试自动检测压缩格式
                        content = self.auto_decompress(raw_content)
                        if not content:
                            content = response.text
                            logger.info(f"使用原始文本内容，长度: {len(content)}")
                        else:
                            logger.info(f"自动解压缩成功，长度: {len(content)}")
                    
                    # 保存解压缩后的内容
                    with open(f'douyin_properly_decompressed_{int(time.time())}.html', 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    return content
                    
                except Exception as decompress_error:
                    logger.error(f"解压缩失败: {decompress_error}")
                    # 如果解压缩失败，尝试使用原始内容
                    content = response.text
                    logger.info(f"使用原始内容，长度: {len(content)}")
                    return content
            else:
                logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取页面内容失败: {e}")
            return None

    def auto_decompress(self, raw_content):
        """自动检测并解压缩内容"""
        try:
            # 尝试gzip
            try:
                return gzip.decompress(raw_content).decode('utf-8')
            except:
                pass
            
            # 尝试brotli
            try:
                return brotli.decompress(raw_content).decode('utf-8')
            except:
                pass
            
            # 尝试zlib/deflate
            try:
                import zlib
                return zlib.decompress(raw_content).decode('utf-8')
            except:
                pass
            
            # 尝试zlib with -15 window bits (raw deflate)
            try:
                import zlib
                return zlib.decompress(raw_content, -15).decode('utf-8')
            except:
                pass
                
        except Exception as e:
            logger.debug(f"自动解压缩失败: {e}")
        
        return None

    def try_multiple_api_approaches(self, video_id):
        """尝试多种API方法"""
        if not video_id:
            return None
        
        # 方法1: 标准Web API
        result = self.try_web_api(video_id)
        if result:
            return result
        
        # 方法2: 移动端API
        result = self.try_mobile_api(video_id)
        if result:
            return result
        
        # 方法3: 带参数的API
        result = self.try_parameterized_api(video_id)
        if result:
            return result
        
        return None

    def try_web_api(self, video_id):
        """尝试Web API"""
        api_urls = [
            f"https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids={video_id}",
            f"https://www.douyin.com/aweme/v1/web/aweme/detail/?aweme_id={video_id}",
        ]
        
        for api_url in api_urls:
            try:
                headers = self.headers.copy()
                headers['Referer'] = 'https://www.douyin.com/'
                headers['X-Requested-With'] = 'XMLHttpRequest'
                
                response = self.session.get(api_url, headers=headers, timeout=15)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"Web API请求成功: {api_url}")
                        
                        # 保存API响应
                        with open(f'douyin_web_api_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        result = self.parse_api_response(data)
                        if result:
                            return result
                    except json.JSONDecodeError:
                        logger.debug(f"Web API响应不是JSON格式")
                        
            except Exception as e:
                logger.debug(f"Web API请求失败: {e}")
        
        return None

    def try_mobile_api(self, video_id):
        """尝试移动端API"""
        mobile_headers = self.headers.copy()
        mobile_headers['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        
        api_urls = [
            f"https://m.douyin.com/web/api/v2/aweme/iteminfo/?item_ids={video_id}",
            f"https://aweme.snssdk.com/aweme/v1/feed/?aweme_id={video_id}",
        ]
        
        for api_url in api_urls:
            try:
                response = self.session.get(api_url, headers=mobile_headers, timeout=15)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"移动端API请求成功: {api_url}")
                        
                        # 保存API响应
                        with open(f'douyin_mobile_api_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        result = self.parse_api_response(data)
                        if result:
                            return result
                    except json.JSONDecodeError:
                        logger.debug(f"移动端API响应不是JSON格式")
                        
            except Exception as e:
                logger.debug(f"移动端API请求失败: {e}")
        
        return None

    def try_parameterized_api(self, video_id):
        """尝试带参数的API"""
        timestamp = int(time.time())
        
        params = {
            'aweme_id': video_id,
            'aid': '1128',
            'app_name': 'douyin_web',
            'device_platform': 'webapp',
            'pc_client_type': '1',
            'version_code': '170400',
            'version_name': '17.4.0',
            'channel': 'channel_pc_web',
            'update_version_code': '170400',
            'webid': '7' + str(random.randint(100000000000000000, 999999999999999999)),
            'msToken': self.generate_ms_token(),
        }
        
        api_url = "https://www.douyin.com/aweme/v1/web/aweme/detail/"
        
        try:
            headers = self.headers.copy()
            headers['Referer'] = 'https://www.douyin.com/'
            
            response = self.session.get(api_url, params=params, headers=headers, timeout=15)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.info(f"参数化API请求成功")
                    
                    # 保存API响应
                    with open(f'douyin_param_api_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    result = self.parse_api_response(data)
                    if result:
                        return result
                except json.JSONDecodeError:
                    logger.debug(f"参数化API响应不是JSON格式")
                    
        except Exception as e:
            logger.debug(f"参数化API请求失败: {e}")
        
        return None

    def generate_ms_token(self):
        """生成msToken"""
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
        return ''.join(random.choice(chars) for _ in range(107))

    def parse_html_content_advanced(self, html):
        """高级HTML内容解析"""
        try:
            # 检查HTML是否有效
            if len(html) < 100:
                logger.warning(f"HTML内容太短，可能无效: {len(html)}")
                return None
            
            # 查找各种可能的数据结构
            patterns = [
                (r'window\.__INITIAL_STATE__\s*=\s*({.+?});', 'INITIAL_STATE'),
                (r'window\._ROUTER_DATA\s*=\s*({.+?});', 'ROUTER_DATA'),
                (r'window\.RENDER_DATA\s*=\s*"([^"]+)"', 'RENDER_DATA_ENCODED'),
                (r'window\.RENDER_DATA\s*=\s*({.+?});', 'RENDER_DATA'),
                (r'window\.SIGI_STATE\s*=\s*({.+?});', 'SIGI_STATE'),
                (r'self\.__pace_f\.push\(\[1,"([^"]+)"\]\)', 'PACE_DATA'),
                (r'<script[^>]*id="RENDER_DATA"[^>]*>([^<]+)</script>', 'RENDER_SCRIPT'),
                (r'"videoObject"\s*:\s*({.+?})', 'VIDEO_OBJECT'),
                (r'"aweme_detail"\s*:\s*({.+?})', 'AWEME_DETAIL'),
                (r'"itemInfo"\s*:\s*({.+?})', 'ITEM_INFO'),
                (r'"aweme"\s*:\s*({.+?})', 'AWEME'),
            ]
            
            for pattern, name in patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                for i, match in enumerate(matches):
                    try:
                        if name in ['RENDER_DATA_ENCODED', 'PACE_DATA']:
                            # 尝试解码
                            decoded_data = self.advanced_decode_data(match)
                            if decoded_data:
                                data = json.loads(decoded_data)
                            else:
                                continue
                        else:
                            data = json.loads(match)
                        
                        logger.info(f"从HTML解析到数据: {name}-{i+1}")
                        
                        # 保存调试数据
                        with open(f'douyin_html_{name}_{i+1}_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        # 解析视频信息
                        video_info = self.parse_api_response(data)
                        if video_info:
                            return video_info
                    
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.debug(f"JSON解析失败 ({name}-{i+1}): {e}")
                        continue
            
            # 如果没有找到结构化数据，尝试寻找可能的视频URL
            video_urls = self.extract_video_urls_from_html(html)
            if video_urls:
                return {'video_url': video_urls[0], 'title': 'Unknown', 'author': 'Unknown'}
        
        except Exception as e:
            logger.error(f"高级HTML解析失败: {e}")
        
        return None

    def advanced_decode_data(self, encoded_data):
        """高级数据解码"""
        try:
            # 方法1: URL解码
            try:
                decoded = unquote(encoded_data)
                if decoded != encoded_data and self.is_valid_json(decoded):
                    return decoded
            except:
                pass
            
            # 方法2: Base64解码
            try:
                decoded = base64.b64decode(encoded_data).decode('utf-8')
                if self.is_valid_json(decoded):
                    return decoded
            except:
                pass
            
            # 方法3: 双重URL解码
            try:
                decoded = unquote(unquote(encoded_data))
                if self.is_valid_json(decoded):
                    return decoded
            except:
                pass
            
            # 方法4: 尝试其他编码
            try:
                decoded = encoded_data.encode('latin1').decode('utf-8')
                if self.is_valid_json(decoded):
                    return decoded
            except:
                pass
                
        except Exception as e:
            logger.debug(f"高级解码失败: {e}")
        
        return None

    def extract_video_urls_from_html(self, html):
        """从HTML中提取视频URL"""
        video_urls = []
        
        # 查找可能的视频URL模式
        url_patterns = [
            r'https?://[^"\']*\.mp4[^"\']*',
            r'https?://[^"\']*video[^"\']*\.mp4',
            r'https?://aweme[^"\']*\.mp4',
            r'https?://v[0-9]+[^"\']*\.douyin[^"\']*\.mp4',
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, html)
            for match in matches:
                if match not in video_urls:
                    video_urls.append(match)
                    logger.info(f"找到可能的视频URL: {match}")
        
        return video_urls

    def is_valid_json(self, text):
        """检查是否是有效的JSON"""
        try:
            json.loads(text)
            return True
        except:
            return False

    def parse_api_response(self, data):
        """解析API响应"""
        try:
            video_info = self.extract_video_info_recursive(data)
            if video_info and len(video_info) >= 2:
                return video_info
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
        return None

    def extract_video_info_recursive(self, obj, depth=0, max_depth=15):
        """递归提取视频信息"""
        if depth > max_depth:
            return None
        
        video_info = {}
        
        if isinstance(obj, dict):
            # 查找关键字段
            field_mappings = {
                'title': ['desc', 'title', 'content', 'text', 'share_desc', 'aweme_desc'],
                'author': ['nickname', 'author', 'username', 'unique_id', 'sec_uid'],
                'cover': ['cover', 'thumbnail', 'poster', 'dynamic_cover', 'origin_cover'],
                'video_url': ['play_url', 'download_url', 'video_url', 'play_addr', 'play_url_list'],
                'video_id': ['aweme_id', 'item_id', 'video_id', 'id'],
                'duration': ['duration', 'video_duration'],
            }
            
            for info_key, possible_keys in field_mappings.items():
                for key in possible_keys:
                    if key in obj and obj[key]:
                        value = obj[key]
                        if info_key in ['video_url', 'cover']:
                            url = self.extract_url_from_value(value)
                            if url:
                                video_info[info_key] = url
                        else:
                            video_info[info_key] = str(value)
                        break
            
            # 如果找到了足够的信息，返回
            if len(video_info) >= 3:
                return video_info
            
            # 递归搜索
            for key, value in obj.items():
                if isinstance(value, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(value, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        elif isinstance(obj, list):
            for item in obj:
                if isinstance(item, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(item, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        return video_info if len(video_info) >= 2 else None

    def extract_url_from_value(self, value):
        """从值中提取URL"""
        if isinstance(value, str) and value.startswith(('http', '//')):
            return value
        elif isinstance(value, dict):
            url_keys = ['url', 'uri', 'src', 'href', 'play_url', 'download_url']
            for key in url_keys:
                if key in value:
                    url_value = value[key]
                    if isinstance(url_value, str) and url_value.startswith(('http', '//')):
                        return url_value
                    elif isinstance(url_value, list) and url_value:
                        return self.extract_url_from_value(url_value[0])
        elif isinstance(value, list) and value:
            return self.extract_url_from_value(value[0])
        
        return None

    def parse_video(self, url):
        """解析视频的主方法"""
        logger.info(f"🚀 开始解析抖音视频: {url}")
        
        # 1. 获取真实URL
        real_url = self.get_real_url(url)
        
        # 2. 提取视频ID
        video_id = self.extract_video_id(real_url)
        
        # 3. 尝试多种API方法
        logger.info("🔍 尝试多种API方法...")
        result = self.try_multiple_api_approaches(video_id)
        if result:
            return result
        
        # 4. 获取并解析页面内容
        logger.info("📄 获取并解析页面内容...")
        html_content = self.get_page_content_with_proper_decompression(real_url)
        if html_content:
            result = self.parse_html_content_advanced(html_content)
            if result:
                return result
        
        logger.error("❌ 所有解析方法都失败了")
        return None

def main():
    """主函数"""
    test_url = "https://v.douyin.com/5UheTSjzZx0/"
    
    print("🎬 抖音视频解析器 - 专业版本")
    print("=" * 60)
    
    spider = DouyinProfessionalSpider()
    result = spider.parse_video(test_url)
    
    if result:
        print("\n🎉 解析成功！视频信息如下：")
        print("=" * 60)
        for key, value in result.items():
            if key == 'video_url':
                print(f"🎥 视频链接: {value}")
            elif key == 'title':
                print(f"📝 标题: {value}")
            elif key == 'author':
                print(f"👤 作者: {value}")
            elif key == 'cover':
                print(f"🖼️  封面: {value}")
            else:
                print(f"📋 {key}: {value}")
        print("=" * 60)
        
        if 'video_url' in result:
            print(f"\n✅ 视频直链获取成功！")
            print(f"🔗 下载链接: {result['video_url']}")
            print("💡 可以直接使用此链接下载视频")
    else:
        print("\n❌ 解析失败")
        print("💡 建议检查生成的调试文件获取更多信息")

if __name__ == "__main__":
    main()
