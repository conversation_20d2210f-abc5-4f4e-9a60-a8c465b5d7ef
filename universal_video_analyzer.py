#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用视频平台逆向分析工具
专门用于分析各种视频平台的链接结构和数据获取方法
支持抖音、微视、快手、小红书等主流平台
"""

import requests
import re
import json
import time
import hashlib
import base64
import gzip
import brotli
from urllib.parse import urlparse, parse_qs, unquote, quote
from fake_useragent import UserAgent
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UniversalVideoAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.139 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="139", "Google Chrome";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Connection': 'keep-alive'
        }
        self.session.headers.update(self.headers)

    def detect_platform(self, url):
        """检测视频平台"""
        platform_patterns = {
            'douyin': [r'douyin\.com', r'dy\.com', r'iesdouyin\.com'],
            'weishi': [r'weishi\.qq\.com', r'isee\.weishi\.qq\.com'],
            'kuaishou': [r'kuaishou\.com', r'ks\.com'],
            'xiaohongshu': [r'xiaohongshu\.com', r'xhslink\.com'],
            'bilibili': [r'bilibili\.com', r'b23\.tv'],
            'weibo': [r'weibo\.com', r'weibo\.cn'],
        }
        
        for platform, patterns in platform_patterns.items():
            for pattern in patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    return platform
        
        return 'unknown'

    def comprehensive_url_analysis(self, url):
        """全面的URL分析"""
        logger.info(f"🔍 开始全面分析URL: {url}")
        
        # 1. 平台检测
        platform = self.detect_platform(url)
        logger.info(f"📱 检测到平台: {platform}")
        
        # 2. URL结构分析
        url_info = self.analyze_url_structure(url)
        
        # 3. 获取真实URL
        real_url = self.get_real_url(url)
        
        # 4. 页面内容分析
        page_analysis = self.analyze_page_content(real_url)
        
        # 5. JavaScript分析
        js_analysis = self.analyze_javascript_resources(page_analysis.get('html', ''))
        
        # 6. 网络请求分析
        network_analysis = self.analyze_network_requests(real_url, platform)
        
        # 7. 数据提取尝试
        extraction_results = self.attempt_data_extraction(page_analysis, js_analysis, network_analysis, platform)
        
        # 8. 生成分析报告
        report = self.generate_analysis_report(url, platform, url_info, page_analysis, js_analysis, network_analysis, extraction_results)
        
        return report

    def analyze_url_structure(self, url):
        """分析URL结构"""
        try:
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            
            # 提取可能的视频ID
            video_id_patterns = [
                r'/video/(\w+)',
                r'/share/video/(\w+)',
                r'id=(\w+)',
                r'vid=(\w+)',
                r'aweme_id=(\w+)',
                r'/(\w+)$'
            ]
            
            video_ids = []
            for pattern in video_id_patterns:
                matches = re.findall(pattern, url)
                video_ids.extend(matches)
            
            return {
                'domain': parsed.netloc,
                'path': parsed.path,
                'query_params': query_params,
                'possible_video_ids': list(set(video_ids)),
                'scheme': parsed.scheme
            }
        except Exception as e:
            logger.error(f"URL结构分析失败: {e}")
            return {}

    def get_real_url(self, url):
        """获取真实URL"""
        try:
            response = self.session.head(url, allow_redirects=True, timeout=15)
            real_url = response.url
            logger.info(f"🔗 真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            return url

    def analyze_page_content(self, url):
        """分析页面内容"""
        try:
            logger.info("📄 分析页面内容...")
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code != 200:
                return {'error': f'HTTP {response.status_code}'}
            
            # 处理压缩内容
            html = self.decompress_content(response)
            
            # 保存原始HTML
            timestamp = int(time.time())
            with open(f'analysis_page_{timestamp}.html', 'w', encoding='utf-8') as f:
                f.write(html)
            
            # 分析HTML结构
            analysis = {
                'html': html,
                'length': len(html),
                'title': self.extract_title(html),
                'meta_tags': self.extract_meta_tags(html),
                'scripts': self.extract_script_tags(html),
                'json_data': self.extract_json_data(html),
                'api_endpoints': self.find_api_endpoints(html),
                'video_urls': self.find_video_urls(html),
                'timestamp': timestamp
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"页面内容分析失败: {e}")
            return {'error': str(e)}

    def decompress_content(self, response):
        """解压缩内容"""
        try:
            content_encoding = response.headers.get('content-encoding', '').lower()
            
            if content_encoding == 'gzip':
                return gzip.decompress(response.content).decode('utf-8')
            elif content_encoding == 'br':
                return brotli.decompress(response.content).decode('utf-8')
            elif content_encoding == 'deflate':
                import zlib
                return zlib.decompress(response.content).decode('utf-8')
            else:
                return response.text
        except Exception as e:
            logger.debug(f"解压缩失败: {e}")
            return response.text

    def extract_title(self, html):
        """提取页面标题"""
        match = re.search(r'<title[^>]*>([^<]+)</title>', html, re.IGNORECASE)
        return match.group(1).strip() if match else None

    def extract_meta_tags(self, html):
        """提取meta标签"""
        meta_tags = {}
        patterns = [
            (r'<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\']', 'og_title'),
            (r'<meta[^>]*property=["\']og:description["\'][^>]*content=["\']([^"\']+)["\']', 'og_description'),
            (r'<meta[^>]*property=["\']og:video["\'][^>]*content=["\']([^"\']+)["\']', 'og_video'),
            (r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', 'description'),
        ]
        
        for pattern, key in patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                meta_tags[key] = match.group(1)
        
        return meta_tags

    def extract_script_tags(self, html):
        """提取script标签"""
        script_patterns = [
            r'<script[^>]*src=["\']([^"\']+)["\']',
            r'<script[^>]*>(.*?)</script>'
        ]
        
        scripts = {'external': [], 'inline': []}
        
        # 外部脚本
        for match in re.finditer(script_patterns[0], html, re.IGNORECASE):
            scripts['external'].append(match.group(1))
        
        # 内联脚本
        for match in re.finditer(script_patterns[1], html, re.IGNORECASE | re.DOTALL):
            content = match.group(1).strip()
            if len(content) > 50:  # 只保存较长的脚本
                scripts['inline'].append(content[:1000])  # 截取前1000字符
        
        return scripts

    def extract_json_data(self, html):
        """提取JSON数据"""
        json_patterns = [
            (r'window\.__INITIAL_STATE__\s*=\s*({.+?});', 'INITIAL_STATE'),
            (r'window\._ROUTER_DATA\s*=\s*({.+?});', 'ROUTER_DATA'),
            (r'window\.RENDER_DATA\s*=\s*"([^"]+)"', 'RENDER_DATA_ENCODED'),
            (r'window\.Vise\.initState\s*=\s*({.+?});', 'VISE_INIT_STATE'),
            (r'window\.SIGI_STATE\s*=\s*({.+?});', 'SIGI_STATE'),
        ]
        
        json_data = {}
        for pattern, name in json_patterns:
            matches = re.findall(pattern, html, re.DOTALL)
            if matches:
                for i, match in enumerate(matches):
                    try:
                        if name == 'RENDER_DATA_ENCODED':
                            decoded = unquote(match)
                            data = json.loads(decoded)
                        else:
                            data = json.loads(match)
                        
                        json_data[f"{name}_{i+1}"] = data
                        
                        # 保存JSON数据
                        timestamp = int(time.time())
                        with open(f'analysis_json_{name}_{i+1}_{timestamp}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                    except json.JSONDecodeError:
                        continue
        
        return json_data

    def find_api_endpoints(self, html):
        """查找API端点"""
        api_patterns = [
            r'https?://[^"\']*api[^"\']*',
            r'https?://[^"\']*cgi-bin[^"\']*',
            r'https?://[^"\']*trpc[^"\']*',
            r'/api/[^"\']*',
            r'/cgi-bin/[^"\']*',
        ]
        
        apis = set()
        for pattern in api_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            apis.update(matches)
        
        return list(apis)

    def find_video_urls(self, html):
        """查找视频URL"""
        video_patterns = [
            r'https?://[^"\']*\.mp4[^"\']*',
            r'https?://[^"\']*video[^"\']*\.mp4',
            r'https?://[^"\']*\.m3u8[^"\']*',
            r'"play_url"\s*:\s*"([^"]+)"',
            r'"video_url"\s*:\s*"([^"]+)"',
            r'"download_url"\s*:\s*"([^"]+)"',
        ]
        
        videos = set()
        for pattern in video_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            videos.update(matches)
        
        return list(videos)

    def analyze_javascript_resources(self, html):
        """分析JavaScript资源"""
        try:
            logger.info("🔧 分析JavaScript资源...")
            
            # 提取外部JS文件
            js_files = re.findall(r'<script[^>]*src=["\']([^"\']+\.js[^"\']*)["\']', html, re.IGNORECASE)
            
            js_analysis = {'files': [], 'apis_found': []}
            
            for js_url in js_files[:5]:  # 限制分析前5个JS文件
                try:
                    if not js_url.startswith('http'):
                        continue
                    
                    logger.info(f"分析JS文件: {js_url}")
                    response = self.session.get(js_url, timeout=10)
                    
                    if response.status_code == 200:
                        js_content = response.text
                        
                        # 在JS中查找API端点
                        api_patterns = [
                            r'["\']([^"\']*api[^"\']*video[^"\']*)["\']',
                            r'["\']([^"\']*cgi-bin[^"\']*)["\']',
                            r'["\']([^"\']*feeds[^"\']*)["\']',
                        ]
                        
                        found_apis = []
                        for pattern in api_patterns:
                            matches = re.findall(pattern, js_content)
                            found_apis.extend(matches)
                        
                        js_analysis['files'].append({
                            'url': js_url,
                            'size': len(js_content),
                            'apis': found_apis
                        })
                        
                        js_analysis['apis_found'].extend(found_apis)
                        
                except Exception as e:
                    logger.debug(f"分析JS文件失败: {e}")
            
            return js_analysis
            
        except Exception as e:
            logger.error(f"JavaScript资源分析失败: {e}")
            return {}

    def analyze_network_requests(self, url, platform):
        """分析网络请求"""
        try:
            logger.info("🌐 分析网络请求...")
            
            # 根据平台构造可能的API请求
            platform_apis = {
                'douyin': [
                    'https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/',
                    'https://www.douyin.com/aweme/v1/web/aweme/detail/',
                ],
                'weishi': [
                    'https://api.weishi.qq.com/trpc.weishi.weishi_h5_proxy.weishi_h5_proxy/GetVideoByVid',
                    'https://isee.weishi.qq.com/cgi-bin/h5/video/get_video_info',
                ],
                'kuaishou': [
                    'https://www.kuaishou.com/graphql',
                ],
            }
            
            network_analysis = {'attempted_apis': [], 'successful_apis': []}
            
            apis = platform_apis.get(platform, [])
            for api_url in apis:
                try:
                    logger.info(f"测试API: {api_url}")
                    
                    headers = self.headers.copy()
                    headers['Referer'] = url
                    
                    response = self.session.get(api_url, headers=headers, timeout=10)
                    
                    result = {
                        'url': api_url,
                        'status_code': response.status_code,
                        'headers': dict(response.headers),
                        'content_length': len(response.content)
                    }
                    
                    if response.status_code == 200:
                        network_analysis['successful_apis'].append(result)
                    
                    network_analysis['attempted_apis'].append(result)
                    
                except Exception as e:
                    logger.debug(f"API测试失败: {e}")
            
            return network_analysis
            
        except Exception as e:
            logger.error(f"网络请求分析失败: {e}")
            return {}

    def attempt_data_extraction(self, page_analysis, js_analysis, network_analysis, platform):
        """尝试数据提取"""
        try:
            logger.info("🎯 尝试数据提取...")
            
            extraction_results = {
                'title': None,
                'author': None,
                'video_url': None,
                'cover_url': None,
                'extraction_methods': []
            }
            
            # 方法1: 从页面meta标签提取
            meta_tags = page_analysis.get('meta_tags', {})
            if meta_tags.get('og_title'):
                extraction_results['title'] = meta_tags['og_title']
                extraction_results['extraction_methods'].append('meta_tags')
            
            # 方法2: 从JSON数据提取
            json_data = page_analysis.get('json_data', {})
            for key, data in json_data.items():
                title = self.deep_search(data, ['title', 'desc', 'content'])
                author = self.deep_search(data, ['nickname', 'author', 'username'])
                video_url = self.deep_search(data, ['play_url', 'video_url', 'download_url'])
                
                if title and not extraction_results['title']:
                    extraction_results['title'] = title
                    extraction_results['extraction_methods'].append(f'json_{key}')
                
                if author and not extraction_results['author']:
                    extraction_results['author'] = author
                    extraction_results['extraction_methods'].append(f'json_{key}')
                
                if video_url and not extraction_results['video_url']:
                    extraction_results['video_url'] = video_url
                    extraction_results['extraction_methods'].append(f'json_{key}')
            
            # 方法3: 从页面直接搜索视频URL
            video_urls = page_analysis.get('video_urls', [])
            if video_urls and not extraction_results['video_url']:
                extraction_results['video_url'] = video_urls[0]
                extraction_results['extraction_methods'].append('direct_search')
            
            return extraction_results
            
        except Exception as e:
            logger.error(f"数据提取失败: {e}")
            return {}

    def deep_search(self, obj, keys, depth=0, max_depth=10):
        """深度搜索对象中的键值"""
        if depth > max_depth or not isinstance(obj, (dict, list)):
            return None
        
        if isinstance(obj, dict):
            for key in keys:
                if key in obj and obj[key]:
                    value = obj[key]
                    if isinstance(value, str) and value.strip():
                        return value.strip()
                    elif isinstance(value, list) and value:
                        return str(value[0]).strip()
            
            # 递归搜索
            for value in obj.values():
                result = self.deep_search(value, keys, depth + 1, max_depth)
                if result:
                    return result
        
        elif isinstance(obj, list):
            for item in obj:
                result = self.deep_search(item, keys, depth + 1, max_depth)
                if result:
                    return result
        
        return None

    def generate_analysis_report(self, url, platform, url_info, page_analysis, js_analysis, network_analysis, extraction_results):
        """生成分析报告"""
        timestamp = int(time.time())
        
        report = {
            'analysis_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'original_url': url,
            'platform': platform,
            'url_structure': url_info,
            'page_analysis': {
                'title': page_analysis.get('title'),
                'html_length': page_analysis.get('length'),
                'json_data_count': len(page_analysis.get('json_data', {})),
                'api_endpoints_found': len(page_analysis.get('api_endpoints', [])),
                'video_urls_found': len(page_analysis.get('video_urls', [])),
            },
            'javascript_analysis': {
                'files_analyzed': len(js_analysis.get('files', [])),
                'apis_found': len(js_analysis.get('apis_found', [])),
            },
            'network_analysis': {
                'apis_tested': len(network_analysis.get('attempted_apis', [])),
                'successful_apis': len(network_analysis.get('successful_apis', [])),
            },
            'extraction_results': extraction_results,
            'recommendations': self.generate_recommendations(platform, extraction_results),
        }
        
        # 保存完整报告
        with open(f'analysis_report_{timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report

    def generate_recommendations(self, platform, extraction_results):
        """生成建议"""
        recommendations = []
        
        if not extraction_results.get('video_url'):
            recommendations.append("未能提取到视频URL，建议：")
            recommendations.append("1. 使用浏览器开发者工具监控网络请求")
            recommendations.append("2. 分析页面的JavaScript代码")
            recommendations.append("3. 尝试模拟移动端访问")
            recommendations.append("4. 使用代理IP和更真实的请求头")
        
        if platform == 'unknown':
            recommendations.append("未识别的平台，建议手动分析URL结构")
        
        return recommendations

def main():
    """主函数"""
    test_urls = [
        "https://v.douyin.com/5UheTSjzZx0/",
        "https://video.weishi.qq.com/qvW4Kldp"
    ]
    
    analyzer = UniversalVideoAnalyzer()
    
    for url in test_urls:
        print(f"\n{'='*80}")
        print(f"🔍 分析URL: {url}")
        print('='*80)
        
        report = analyzer.comprehensive_url_analysis(url)
        
        print(f"\n📊 分析报告:")
        print(f"平台: {report['platform']}")
        print(f"页面标题: {report['page_analysis']['title']}")
        print(f"HTML长度: {report['page_analysis']['html_length']}")
        print(f"JSON数据块: {report['page_analysis']['json_data_count']}")
        print(f"发现的API端点: {report['page_analysis']['api_endpoints_found']}")
        print(f"发现的视频URL: {report['page_analysis']['video_urls_found']}")
        
        print(f"\n🎯 提取结果:")
        for key, value in report['extraction_results'].items():
            if key != 'extraction_methods' and value:
                print(f"{key}: {value}")
        
        if report['extraction_results']['extraction_methods']:
            print(f"提取方法: {', '.join(report['extraction_results']['extraction_methods'])}")
        
        if report['recommendations']:
            print(f"\n💡 建议:")
            for rec in report['recommendations']:
                print(f"  {rec}")

if __name__ == "__main__":
    main()
