#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频解析器 - 使用Python专业爬虫库
目标：获取视频的CDN直链、作者、文案、封面等信息
"""

import requests
import re
import json
import time
import random
from urllib.parse import urlparse, parse_qs, unquote
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from fake_useragent import UserAgent
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DouyinSpider:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_headers()

    def setup_headers(self):
        """设置PC端请求头"""
        self.headers = {
            'User-Agent': self.ua.chrome,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Connection': 'keep-alive',
            'Referer': 'https://www.douyin.com/'
        }
        self.session.headers.update(self.headers)

    def get_real_url(self, short_url):
        """获取短链接的真实URL"""
        try:
            response = self.session.head(short_url, allow_redirects=True, timeout=10)
            real_url = response.url
            logger.info(f"真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            return short_url

    def extract_video_id(self, url):
        """从URL中提取视频ID"""
        patterns = [
            r'/video/(\d+)',
            r'modal_id=(\d+)',
            r'aweme_id=(\d+)',
            r'/share/video/(\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                video_id = match.group(1)
                logger.info(f"提取到视频ID: {video_id}")
                return video_id
        
        logger.warning("未能提取到视频ID")
        return None

    def parse_with_selenium(self, url):
        """使用undetected-chromedriver解析页面"""
        options = uc.ChromeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        # 移除可能导致问题的选项
        # options.add_experimental_option("excludeSwitches", ["enable-automation"])
        # options.add_experimental_option('useAutomationExtension', False)

        driver = None
        try:
            driver = uc.Chrome(options=options, version_main=None)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.set_page_load_timeout(30)

            logger.info(f"正在访问页面: {url}")
            driver.get(url)

            # 等待页面加载并模拟人类行为
            time.sleep(random.uniform(3, 6))

            # 滚动页面模拟真实用户行为
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(random.uniform(1, 3))
            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(random.uniform(1, 2))

            # 尝试获取页面源码
            html = driver.page_source
            logger.info(f"获取到HTML长度: {len(html)}")

            # 保存HTML用于调试
            with open('douyin_debug.html', 'w', encoding='utf-8') as f:
                f.write(html)

            # 监听网络请求
            network_data = self.capture_network_requests(driver)
            if network_data:
                logger.info("捕获到网络请求数据")
                return network_data

            # 尝试执行JavaScript获取数据
            video_data = self.extract_video_data_from_js(driver)
            if video_data:
                return video_data

            # 尝试从HTML中解析数据
            return self.parse_html_content(html)

        except Exception as e:
            logger.error(f"Selenium解析失败: {e}")
            return None
        finally:
            if driver:
                driver.quit()

    def capture_network_requests(self, driver):
        """捕获网络请求中的视频数据"""
        try:
            # 获取网络日志
            logs = driver.get_log('performance')
            for log in logs:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.responseReceived':
                    url = message['message']['params']['response']['url']
                    if any(keyword in url for keyword in ['aweme', 'video', 'play', 'api']):
                        logger.info(f"发现可能的API请求: {url}")
                        # 尝试获取响应内容
                        try:
                            response_data = driver.execute_cdp_cmd('Network.getResponseBody',
                                                                 {'requestId': message['message']['params']['requestId']})
                            if response_data and 'body' in response_data:
                                data = json.loads(response_data['body'])
                                video_info = self.parse_api_response(data)
                                if video_info:
                                    return video_info
                        except Exception as e:
                            logger.debug(f"获取响应体失败: {e}")
        except Exception as e:
            logger.debug(f"网络请求捕获失败: {e}")
        return None

    def parse_api_response(self, data):
        """解析API响应中的视频信息"""
        try:
            # 保存API响应用于调试
            with open(f'douyin_api_response_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 尝试解析视频信息
            video_info = self.search_video_fields(data)
            return video_info
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
        return None

    def extract_video_data_from_js(self, driver):
        """从JavaScript变量中提取视频数据"""
        js_scripts = [
            "return window.__INITIAL_STATE__",
            "return window._ROUTER_DATA",
            "return window.RENDER_DATA",
            "return window.__NUXT__",
            "return window.SSR_HYDRATED_DATA",
            "return window.__INITIAL_PROPS__",
            "return window.SIGI_STATE",
            "return window.__APOLLO_STATE__",
            "return window.pageProps",
            # 尝试获取所有全局变量
            "return Object.keys(window).filter(key => key.includes('STATE') || key.includes('DATA') || key.includes('PROPS')).map(key => ({key, value: window[key]}))",
            # 尝试获取页面中的JSON数据
            """
            var scripts = document.querySelectorAll('script');
            var jsonData = [];
            for(var i = 0; i < scripts.length; i++) {
                var text = scripts[i].textContent;
                if(text.includes('aweme') || text.includes('video') || text.includes('play_url')) {
                    try {
                        var matches = text.match(/\\{[^{}]*(?:\\{[^{}]*\\}[^{}]*)*\\}/g);
                        if(matches) {
                            for(var j = 0; j < matches.length; j++) {
                                try {
                                    var parsed = JSON.parse(matches[j]);
                                    if(parsed && (parsed.aweme || parsed.video || parsed.play_url)) {
                                        jsonData.push(parsed);
                                    }
                                } catch(e) {}
                            }
                        }
                    } catch(e) {}
                }
            }
            return jsonData;
            """
        ]

        for i, script in enumerate(js_scripts):
            try:
                result = driver.execute_script(script)
                if result:
                    logger.info(f"从脚本 {i+1} 获取到数据: {type(result)}")
                    # 保存调试数据
                    with open(f'douyin_js_data_{i+1}_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                        json.dump(result, f, ensure_ascii=False, indent=2)

                    # 尝试解析视频信息
                    video_info = self.parse_js_data(result)
                    if video_info:
                        return video_info
            except Exception as e:
                logger.debug(f"执行脚本 {i+1} 失败: {e}")

        return None

    def parse_js_data(self, data):
        """解析JavaScript数据中的视频信息"""
        try:
            if isinstance(data, dict):
                # 递归搜索视频相关字段
                video_info = self.search_video_fields(data)
                if video_info:
                    return video_info
        except Exception as e:
            logger.error(f"解析JS数据失败: {e}")
        
        return None

    def search_video_fields(self, obj, path=""):
        """递归搜索视频相关字段"""
        if isinstance(obj, dict):
            # 检查是否包含视频信息的关键字段
            video_keys = ['play_url', 'download_url', 'video_url', 'play_addr', 'aweme_detail']
            
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                # 如果找到视频相关字段
                if any(vkey in key.lower() for vkey in video_keys):
                    logger.info(f"找到视频字段: {current_path}")
                    
                    # 尝试提取视频信息
                    video_info = self.extract_video_info(obj)
                    if video_info:
                        return video_info
                
                # 递归搜索
                if isinstance(value, (dict, list)):
                    result = self.search_video_fields(value, current_path)
                    if result:
                        return result
        
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                result = self.search_video_fields(item, current_path)
                if result:
                    return result
        
        return None

    def extract_video_info(self, data):
        """从数据中提取视频信息"""
        try:
            video_info = {}

            # 扩展字段映射，包含更多可能的字段名
            fields_mapping = {
                'title': ['desc', 'title', 'content', 'text', 'share_desc', 'aweme_desc', 'video_text'],
                'author': ['nickname', 'author', 'username', 'unique_id', 'sec_uid', 'short_id', 'author_user_id'],
                'cover': ['cover', 'thumbnail', 'poster', 'dynamic_cover', 'origin_cover', 'animated_cover', 'video_cover'],
                'video_url': ['play_url', 'download_url', 'video_url', 'play_addr', 'play_url_list', 'download_addr'],
                'video_id': ['aweme_id', 'item_id', 'video_id', 'id'],
                'duration': ['duration', 'video_duration', 'play_duration'],
                'create_time': ['create_time', 'createTime', 'publish_time']
            }

            for info_key, possible_keys in fields_mapping.items():
                for key in possible_keys:
                    value = self.deep_get(data, key)
                    if value:
                        # 处理URL列表
                        if info_key in ['video_url', 'cover'] and isinstance(value, list):
                            # 选择最高质量的URL
                            video_info[info_key] = self.select_best_url(value)
                        elif info_key in ['video_url', 'cover'] and isinstance(value, dict):
                            # 处理嵌套的URL对象
                            url = self.extract_url_from_object(value)
                            if url:
                                video_info[info_key] = url
                        else:
                            video_info[info_key] = value
                        break

            # 如果找到了视频信息，返回
            if any(video_info.values()):
                logger.info(f"提取到视频信息: {video_info}")
                return video_info

        except Exception as e:
            logger.error(f"提取视频信息失败: {e}")

        return None

    def select_best_url(self, url_list):
        """从URL列表中选择最佳质量的URL"""
        if not url_list:
            return None

        # 如果是字符串列表，返回第一个
        if isinstance(url_list[0], str):
            return url_list[0]

        # 如果是对象列表，寻找最高质量的
        best_url = None
        best_quality = 0

        for url_obj in url_list:
            if isinstance(url_obj, dict):
                # 寻找质量标识
                quality_keys = ['quality', 'definition', 'bitrate', 'width', 'height']
                quality = 0

                for key in quality_keys:
                    if key in url_obj and isinstance(url_obj[key], (int, str)):
                        try:
                            quality = max(quality, int(str(url_obj[key]).replace('p', '')))
                        except:
                            pass

                url = self.extract_url_from_object(url_obj)
                if url and quality >= best_quality:
                    best_quality = quality
                    best_url = url

        return best_url or (url_list[0] if url_list else None)

    def extract_url_from_object(self, obj):
        """从对象中提取URL"""
        if isinstance(obj, str):
            return obj

        if isinstance(obj, dict):
            url_keys = ['url', 'uri', 'src', 'href', 'link', 'play_url', 'download_url']
            for key in url_keys:
                if key in obj:
                    value = obj[key]
                    if isinstance(value, str) and value.startswith(('http', '//')):
                        return value
                    elif isinstance(value, list) and value:
                        return self.extract_url_from_object(value[0])

        return None

    def deep_get(self, obj, key):
        """深度获取对象中的值"""
        if isinstance(obj, dict):
            if key in obj:
                return obj[key]
            
            for value in obj.values():
                if isinstance(value, (dict, list)):
                    result = self.deep_get(value, key)
                    if result:
                        return result
        
        elif isinstance(obj, list):
            for item in obj:
                if isinstance(item, (dict, list)):
                    result = self.deep_get(item, key)
                    if result:
                        return result
        
        return None

    def parse_html_content(self, html):
        """解析HTML内容"""
        try:
            # 扩展的正则表达式模式
            patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                r'window\._ROUTER_DATA\s*=\s*({.+?});',
                r'window\.RENDER_DATA\s*=\s*"([^"]+)"',
                r'window\.RENDER_DATA\s*=\s*({.+?});',
                r'<script[^>]*>.*?window\.__NUXT__\s*=\s*({.+?});.*?</script>',
                r'window\.SIGI_STATE\s*=\s*({.+?});',
                r'window\.__APOLLO_STATE__\s*=\s*({.+?});',
                r'window\.pageProps\s*=\s*({.+?});',
                # 寻找包含aweme或video的JSON对象
                r'"aweme_detail"\s*:\s*({.+?})',
                r'"video"\s*:\s*({.+?})',
                r'"play_url"\s*:\s*({.+?})',
                # 寻找script标签中的JSON数据
                r'<script[^>]*type="application/json"[^>]*>([^<]+)</script>',
                r'<script[^>]*>.*?({[^{}]*"aweme"[^{}]*}).*?</script>',
                r'<script[^>]*>.*?({[^{}]*"video"[^{}]*}).*?</script>',
            ]

            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                for j, match in enumerate(matches):
                    try:
                        if '"([^"]+)"' in pattern and i == 2:  # RENDER_DATA编码情况
                            # 处理编码的JSON
                            decoded = unquote(match)
                            data = json.loads(decoded)
                        else:
                            # 清理匹配的字符串
                            clean_match = match.strip()
                            if not clean_match.startswith('{'):
                                clean_match = '{' + clean_match
                            if not clean_match.endswith('}'):
                                clean_match = clean_match + '}'

                            data = json.loads(clean_match)

                        logger.info(f"从HTML解析到数据，模式 {i+1}-{j+1}: {pattern[:50]}...")

                        # 保存调试数据
                        with open(f'douyin_html_pattern_{i+1}_{j+1}_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)

                        # 尝试解析视频信息
                        video_info = self.parse_js_data(data)
                        if video_info:
                            return video_info

                    except (json.JSONDecodeError, ValueError) as e:
                        logger.debug(f"JSON解析失败 (模式 {i+1}-{j+1}): {e}")
                        continue

            # 如果正则表达式都失败了，尝试寻找可能的API端点
            api_patterns = [
                r'(/aweme/v\d+/[^"\']+)',
                r'(/api/[^"\']*video[^"\']*)',
                r'(https?://[^"\']*aweme[^"\']*)',
                r'(https?://[^"\']*video[^"\']*)',
            ]

            for pattern in api_patterns:
                matches = re.findall(pattern, html)
                for match in matches:
                    logger.info(f"发现可能的API端点: {match}")
                    # 可以尝试直接请求这些API端点
                    api_data = self.try_api_request(match)
                    if api_data:
                        return api_data

        except Exception as e:
            logger.error(f"HTML解析失败: {e}")

        return None

    def try_api_request(self, api_url):
        """尝试请求发现的API端点"""
        try:
            if not api_url.startswith('http'):
                api_url = 'https://www.douyin.com' + api_url

            response = self.session.get(api_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"API请求成功: {api_url}")

                # 保存API响应
                with open(f'douyin_api_direct_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                return self.parse_js_data(data)
        except Exception as e:
            logger.debug(f"API请求失败 {api_url}: {e}")

        return None

    def parse_video(self, url):
        """解析视频信息的主方法"""
        logger.info(f"开始解析视频: {url}")
        
        # 1. 获取真实URL
        real_url = self.get_real_url(url)
        
        # 2. 提取视频ID
        video_id = self.extract_video_id(real_url)
        
        # 3. 使用Selenium解析
        video_info = self.parse_with_selenium(real_url)
        
        if video_info:
            logger.info("解析成功！")
            return video_info
        else:
            logger.error("解析失败，未能获取到视频信息")
            return None

def main():
    """主函数"""
    test_url = "https://v.douyin.com/5UheTSjzZx0/"
    
    spider = DouyinSpider()
    result = spider.parse_video(test_url)
    
    if result:
        print("\n" + "="*50)
        print("解析结果:")
        print("="*50)
        for key, value in result.items():
            print(f"{key}: {value}")
        print("="*50)
    else:
        print("解析失败，请检查日志")

if __name__ == "__main__":
    main()
