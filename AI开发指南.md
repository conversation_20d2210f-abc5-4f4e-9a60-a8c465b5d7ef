# ClearMark 短视频去水印工具 - AI开发指南

## 🎯 项目目标
开发一个基于SpringBoot的短视频去水印工具解析后台，支持抖音、快手、小红书、B站、微博等10个主流平台的视频解析和下载。

## 📋 当前状态总览

### ✅ 已完成功能
- **基础架构**: SpringBoot 3.4.10 + Java 21，完整的安全认证和缓存系统
- **9个平台解析器**: 快手、小红书、B站、微博、皮皮虾、汽水音乐、最右、好看视频等
- **核心服务**: HTTP客户端、JavaScript执行、无头浏览器、HTML解析
- **API接口**: 视频解析、API Key管理、系统管理

### 🔴 核心问题
1. **抖音解析器完全失败** - 反爬虫机制严格，所有方法都被拦截
2. **微视解析器部分失败** - 能获取页面但`feedsList`为空数组

### 🎯 AI助手任务
**专注解决抖音和微视的视频解析问题，获取真实的CDN视频下载链接**

## 🔧 技术架构

### 项目结构
```
src/main/java/cn/zt/claw/clearmark/
├── ClearmarkApplication.java           # 启动类
├── config/                            # 配置类
│   ├── SecurityConfig.java           # 安全配置
│   └── CacheConfig.java              # 缓存配置
├── controller/                        # 控制器
│   ├── VideoParserController.java     # 视频解析API
│   ├── AuthController.java           # 认证API
│   ├── AdminController.java          # 管理API
│   └── TestController.java           # 测试API
├── service/                          # 服务层
│   ├── VideoParserService.java       # 解析服务接口
│   ├── impl/VideoParserServiceImpl.java # 解析服务实现
│   ├── platform/                     # 平台解析器
│   │   ├── PlatformParser.java       # 解析器接口
│   │   ├── DouyinParser.java         # 🔴 抖音解析器(问题)
│   │   ├── WeishiParser.java         # 🟡 微视解析器(问题)
│   │   ├── KuaishouParser.java       # ✅ 快手解析器
│   │   ├── XiaohongshuParser.java    # ✅ 小红书解析器
│   │   ├── BilibiliParser.java       # ✅ B站解析器
│   │   ├── WeiboParser.java          # ✅ 微博解析器
│   │   ├── PipixParser.java          # ✅ 皮皮虾解析器
│   │   ├── QishuiMusicParser.java    # ✅ 汽水音乐解析器
│   │   ├── ZuiyouParser.java         # ✅ 最右解析器
│   │   └── HaokanParser.java         # ✅ 好看视频解析器
│   └── util/                         # 工具服务
│       ├── HttpClientService.java    # HTTP客户端
│       ├── WebDriverService.java     # 无头浏览器
│       ├── JsExecutorService.java    # JS执行引擎
│       └── HtmlParserService.java    # HTML解析
├── model/                            # 数据模型
│   ├── VideoInfo.java               # 视频信息模型
│   ├── ParseRequest.java            # 解析请求模型
│   ├── ParseResponse.java           # 解析响应模型
│   └── PlatformType.java            # 平台类型枚举
└── security/                        # 安全组件
    ├── ApiKeyAuthenticationFilter.java # API Key认证
    └── RateLimitFilter.java         # 限流过滤器
```

### 核心技术栈
- **SpringBoot**: 3.4.10
- **Java**: 21
- **缓存**: Caffeine
- **无头浏览器**: Selenium WebDriver + Chrome
- **JavaScript引擎**: GraalVM
- **HTTP客户端**: 自定义实现

## 🔴 抖音解析器问题详情

### 问题描述
抖音平台反爬虫机制极其严格，所有常规解析方法都失败

### 已尝试的方法
1. **直接API调用** ❌
   - 多个API端点都返回空响应
   - 移动端API也被拦截
   
2. **HTML解析** ❌
   - 普通请求返回压缩/加密内容
   - 无法找到`_ROUTER_DATA`、`_INITIAL_STATE`等关键数据
   
3. **无头浏览器** ❌
   - 能获取完整HTML（917KB）
   - 找到`RENDER_DATA`（150KB JSON）
   - 但只包含页面配置，无视频详情数据

### 技术细节
- **测试链接**: `https://v.douyin.com/5UheTSjzZx0/`
- **真实URL**: `https://www.iesdouyin.com/share/video/7540052813481577763/`
- **视频ID**: `7540052813481577763`
- **问题**: 视频数据通过AJAX动态加载，初始HTML中无视频信息

### 调试文件
- `douyin_html_*.html` - 无头浏览器获取的HTML
- `render_data_*.json` - 解码后的RENDER_DATA
- `douyin_spider.py` - Python爬虫测试脚本

## 🟡 微视解析器问题详情

### 问题描述
微视改版为SPA架构，能获取页面结构但视频数据为空

### 技术细节
- **测试链接**: `https://video.weishi.qq.com/qvW4Kldp`
- **问题**: `window.Vise.initState.feedsList = []`（空数组）
- **状态**: 数据加载状态为true，但无实际视频数据

### 调试文件
- `weishi_html_*.html` - 微视页面HTML

## 🚀 AI助手行动指南

### 优先级1: 抖音解析器
**目标**: 获取视频的真实CDN下载链接

**可尝试的方法**:
1. **深度分析RENDER_DATA**
   - 查看`render_data_*.json`文件
   - 寻找隐藏的视频数据或API端点

2. **网络请求监听**
   - 使用Selenium监听网络请求
   - 捕获AJAX请求的响应

3. **移动端模拟**
   - 模拟移动端浏览器
   - 尝试移动端专用API

4. **逆向工程**
   - 分析抖音的JavaScript代码
   - 找到真实的API调用方式

### 优先级2: 微视解析器
**目标**: 解决SPA架构下的数据获取问题

**可尝试的方法**:
1. **等待策略优化**
   - 增加动态等待时间
   - 监听特定的网络请求完成

2. **JavaScript执行**
   - 主动触发数据加载
   - 模拟用户交互

## 📝 开发规范

### 代码修改原则
1. **保持现有架构** - 不要大幅重构
2. **增量改进** - 在现有基础上优化
3. **详细日志** - 添加调试信息
4. **保存调试文件** - 便于分析问题

### 测试方法
```bash
# 启动应用
./mvnw spring-boot:run

# 测试抖音解析
curl -X POST http://localhost:8080/api/test/douyin \
  -H "Content-Type: application/json" \
  -d '{"url": "https://v.douyin.com/5UheTSjzZx0/"}'

# 测试微视解析
curl http://localhost:8080/api/test/weishi
```

### 成功标准
- **抖音**: 获取到视频的CDN下载链接（支持多清晰度）
- **微视**: 获取到视频标题、作者、下载链接

## 🔍 关键文件位置

### 需要重点关注的文件
- `src/main/java/cn/zt/claw/clearmark/service/platform/DouyinParser.java`
- `src/main/java/cn/zt/claw/clearmark/service/platform/WeishiParser.java`
- `src/main/java/cn/zt/claw/clearmark/service/util/WebDriverService.java`
- `src/main/java/cn/zt/claw/clearmark/controller/TestController.java`

### 配置文件
- `src/main/resources/application.yml` - 应用配置
- `pom.xml` - 依赖管理

## 💡 提示和建议

1. **抖音解析**可能需要：
   - 特殊的请求头或Cookie
   - 签名算法
   - 特定的时间戳或随机数

2. **微视解析**可能需要：
   - 更长的等待时间
   - 特定的JavaScript执行
   - 模拟用户滚动或点击

3. **通用建议**：
   - 多保存调试文件
   - 对比成功平台的实现
   - 考虑使用代理或更换IP

---

**AI助手，开始你的任务吧！专注解决抖音和微视的解析问题，获取真实的视频下载链接！** 🚀
