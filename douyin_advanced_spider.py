#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频解析器 - 高级版本
使用多种方法尝试获取视频信息，包括逆向工程方法
"""

import requests
import re
import json
import time
import random
import gzip
import base64
from urllib.parse import urlparse, parse_qs, unquote, quote
from fake_useragent import UserAgent
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DouyinAdvancedSpider:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_session()
        
    def setup_session(self):
        """设置会话和请求头"""
        # 设置更真实的请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="139", "Google Chrome";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)
        
        # 设置代理（如果需要）
        # self.session.proxies = {'http': 'http://proxy:port', 'https': 'https://proxy:port'}

    def get_real_url(self, short_url):
        """获取短链接的真实URL"""
        try:
            # 先尝试HEAD请求
            response = self.session.head(short_url, allow_redirects=True, timeout=15)
            real_url = response.url
            logger.info(f"真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            # 如果HEAD失败，尝试GET请求
            try:
                response = self.session.get(short_url, allow_redirects=True, timeout=15)
                real_url = response.url
                logger.info(f"通过GET获取真实URL: {real_url}")
                return real_url
            except Exception as e2:
                logger.error(f"GET请求也失败: {e2}")
                return short_url

    def extract_video_id(self, url):
        """从URL中提取视频ID"""
        patterns = [
            r'/video/(\d+)',
            r'modal_id=(\d+)',
            r'aweme_id=(\d+)',
            r'/share/video/(\d+)',
            r'/share/jx-video/(\d+)',
            r'item_ids=(\d+)',
            r'aweme/(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                video_id = match.group(1)
                logger.info(f"提取到视频ID: {video_id}")
                return video_id
        
        logger.warning("未能提取到视频ID")
        return None

    def try_known_api_endpoints(self, video_id):
        """尝试已知的API端点"""
        if not video_id:
            return None
            
        # 已知的API端点列表
        api_endpoints = [
            # Web API
            f"https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids={video_id}",
            f"https://www.douyin.com/aweme/v1/web/aweme/detail/?aweme_id={video_id}",
            f"https://www.douyin.com/aweme/v1/web/aweme/detail/?item_id={video_id}",
            
            # 移动端API
            f"https://aweme.snssdk.com/aweme/v1/feed/?aweme_id={video_id}",
            f"https://aweme.snssdk.com/aweme/v1/aweme/detail/?aweme_id={video_id}",
            
            # 其他可能的端点
            f"https://www.iesdouyin.com/aweme/v1/web/aweme/detail/?aweme_id={video_id}",
            f"https://m.douyin.com/web/api/v2/aweme/iteminfo/?item_ids={video_id}",
        ]
        
        for api_url in api_endpoints:
            try:
                logger.info(f"尝试API: {api_url}")
                
                # 为不同的API使用不同的请求头
                headers = self.headers.copy()
                if 'aweme.snssdk.com' in api_url:
                    headers['User-Agent'] = 'com.ss.android.ugc.aweme/160 (Linux; U; Android 9; zh_CN; MI 6; Build/PKQ1.190118.001; Cronet/TTNetVersion:b4d74d15 2020-04-23 QuicVersion:0144d358 2020-03-24)'
                elif 'm.douyin.com' in api_url:
                    headers['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
                
                response = self.session.get(api_url, headers=headers, timeout=15)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"API请求成功: {api_url}")
                        
                        # 保存调试数据
                        with open(f'douyin_api_success_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        # 解析响应
                        video_info = self.parse_api_response(data)
                        if video_info:
                            return video_info
                    except json.JSONDecodeError:
                        logger.debug(f"API响应不是JSON格式: {api_url}")
                else:
                    logger.debug(f"API请求失败，状态码: {response.status_code}, URL: {api_url}")
                    
            except Exception as e:
                logger.debug(f"API请求异常: {api_url}, 错误: {e}")
        
        return None

    def try_reverse_engineering(self, real_url, video_id):
        """尝试逆向工程方法"""
        logger.info("尝试逆向工程方法...")
        
        # 方法1: 尝试构造不同格式的API请求
        constructed_apis = self.construct_api_urls(video_id, real_url)
        for api_url in constructed_apis:
            try:
                result = self.try_api_request(api_url)
                if result:
                    return result
            except Exception as e:
                logger.debug(f"构造API请求失败: {api_url}, 错误: {e}")
        
        # 方法2: 尝试解析页面中的加密数据
        encrypted_data = self.extract_encrypted_data(real_url)
        if encrypted_data:
            decrypted_info = self.try_decrypt_data(encrypted_data)
            if decrypted_info:
                return decrypted_info
        
        # 方法3: 尝试模拟移动端请求
        mobile_result = self.try_mobile_simulation(real_url, video_id)
        if mobile_result:
            return mobile_result
        
        return None

    def construct_api_urls(self, video_id, real_url):
        """构造可能的API URL"""
        if not video_id:
            return []
        
        # 从URL中提取更多参数
        parsed_url = urlparse(real_url)
        query_params = parse_qs(parsed_url.query)
        
        # 提取可能有用的参数
        aid = query_params.get('from_aid', ['1128'])[0]
        device_id = query_params.get('did', [''])[0]
        
        constructed_urls = [
            f"https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids={video_id}&dytk=",
            f"https://www.douyin.com/aweme/v1/web/aweme/detail/?aweme_id={video_id}&aid={aid}",
            f"https://www.douyin.com/aweme/v1/web/aweme/detail/?aweme_id={video_id}&aid=1128&version_name=23.5.0",
        ]
        
        if device_id:
            constructed_urls.append(f"https://aweme.snssdk.com/aweme/v1/feed/?aweme_id={video_id}&aid={aid}&device_id={device_id}")
        
        return constructed_urls

    def try_api_request(self, api_url):
        """尝试API请求"""
        try:
            headers = self.headers.copy()
            headers['Referer'] = 'https://www.douyin.com/'
            
            response = self.session.get(api_url, headers=headers, timeout=15)
            if response.status_code == 200:
                data = response.json()
                return self.parse_api_response(data)
        except Exception as e:
            logger.debug(f"API请求失败: {api_url}, 错误: {e}")
        return None

    def extract_encrypted_data(self, real_url):
        """提取页面中的加密数据"""
        try:
            response = self.session.get(real_url, timeout=15)
            if response.status_code == 200:
                html = response.text
                
                # 寻找可能的加密数据模式
                patterns = [
                    r'window\.__INITIAL_STATE__\s*=\s*"([^"]+)"',
                    r'window\.RENDER_DATA\s*=\s*"([^"]+)"',
                    r'data-server-rendered="true">([^<]+)</div>',
                    r'<script[^>]*>window\.__NUXT__=([^<]+)</script>',
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, html)
                    for match in matches:
                        if len(match) > 100:  # 只处理较长的数据
                            logger.info(f"找到可能的加密数据，长度: {len(match)}")
                            return match
        except Exception as e:
            logger.error(f"提取加密数据失败: {e}")
        
        return None

    def try_decrypt_data(self, encrypted_data):
        """尝试解密数据"""
        try:
            # 方法1: 尝试URL解码
            try:
                decoded = unquote(encrypted_data)
                if decoded != encrypted_data:
                    data = json.loads(decoded)
                    return self.parse_api_response(data)
            except:
                pass
            
            # 方法2: 尝试Base64解码
            try:
                decoded = base64.b64decode(encrypted_data).decode('utf-8')
                data = json.loads(decoded)
                return self.parse_api_response(data)
            except:
                pass
            
            # 方法3: 尝试直接JSON解析
            try:
                data = json.loads(encrypted_data)
                return self.parse_api_response(data)
            except:
                pass
                
        except Exception as e:
            logger.debug(f"解密数据失败: {e}")
        
        return None

    def try_mobile_simulation(self, real_url, video_id):
        """尝试模拟移动端请求"""
        try:
            mobile_headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Referer': 'https://www.douyin.com/',
            }
            
            # 构造移动端URL
            mobile_url = real_url.replace('www.iesdouyin.com', 'm.douyin.com')
            
            response = self.session.get(mobile_url, headers=mobile_headers, timeout=15)
            if response.status_code == 200:
                html = response.text
                logger.info(f"移动端页面获取成功，长度: {len(html)}")
                
                # 保存移动端HTML
                with open(f'douyin_mobile_{int(time.time())}.html', 'w', encoding='utf-8') as f:
                    f.write(html)
                
                # 尝试解析移动端页面
                return self.parse_mobile_html(html)
                
        except Exception as e:
            logger.error(f"移动端模拟失败: {e}")
        
        return None

    def parse_mobile_html(self, html):
        """解析移动端HTML"""
        try:
            # 移动端可能有不同的数据结构
            patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                r'window\.SIGI_STATE\s*=\s*({.+?});',
                r'"videoObject"\s*:\s*({.+?})',
                r'"itemInfo"\s*:\s*({.+?})',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        result = self.parse_api_response(data)
                        if result:
                            return result
                    except:
                        continue
        except Exception as e:
            logger.error(f"解析移动端HTML失败: {e}")
        
        return None

    def parse_api_response(self, data):
        """解析API响应"""
        try:
            # 递归搜索视频信息
            video_info = self.extract_video_info_recursive(data)
            if video_info and any(video_info.values()):
                logger.info(f"成功提取视频信息: {video_info}")
                return video_info
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
        
        return None

    def extract_video_info_recursive(self, obj, depth=0, max_depth=10):
        """递归提取视频信息"""
        if depth > max_depth:
            return None
        
        video_info = {}
        
        if isinstance(obj, dict):
            # 直接查找关键字段
            field_mappings = {
                'title': ['desc', 'title', 'content', 'text', 'share_desc'],
                'author': ['nickname', 'author', 'username', 'unique_id'],
                'cover': ['cover', 'thumbnail', 'poster', 'dynamic_cover'],
                'video_url': ['play_url', 'download_url', 'video_url', 'play_addr'],
                'video_id': ['aweme_id', 'item_id', 'video_id', 'id'],
            }
            
            for info_key, possible_keys in field_mappings.items():
                for key in possible_keys:
                    if key in obj and obj[key]:
                        value = obj[key]
                        if info_key in ['video_url', 'cover']:
                            url = self.extract_url_from_value(value)
                            if url:
                                video_info[info_key] = url
                        else:
                            video_info[info_key] = str(value)
                        break
            
            # 如果找到了一些信息，返回
            if len(video_info) >= 2:  # 至少要有2个字段
                return video_info
            
            # 递归搜索子对象
            for key, value in obj.items():
                if isinstance(value, (dict, list)):
                    result = self.extract_video_info_recursive(value, depth + 1, max_depth)
                    if result and len(result) >= 2:
                        return result
        
        elif isinstance(obj, list):
            for item in obj:
                if isinstance(item, (dict, list)):
                    result = self.extract_video_info_recursive(item, depth + 1, max_depth)
                    if result and len(result) >= 2:
                        return result
        
        return video_info if video_info else None

    def extract_url_from_value(self, value):
        """从值中提取URL"""
        if isinstance(value, str) and value.startswith(('http', '//')):
            return value
        elif isinstance(value, dict):
            url_keys = ['url', 'uri', 'src', 'href']
            for key in url_keys:
                if key in value and isinstance(value[key], str):
                    return value[key]
        elif isinstance(value, list) and value:
            return self.extract_url_from_value(value[0])
        
        return None

    def parse_video(self, url):
        """解析视频的主方法"""
        logger.info(f"开始解析视频: {url}")
        
        # 1. 获取真实URL
        real_url = self.get_real_url(url)
        
        # 2. 提取视频ID
        video_id = self.extract_video_id(real_url)
        
        # 3. 尝试已知API端点
        logger.info("尝试已知API端点...")
        result = self.try_known_api_endpoints(video_id)
        if result:
            return result
        
        # 4. 尝试逆向工程方法
        logger.info("尝试逆向工程方法...")
        result = self.try_reverse_engineering(real_url, video_id)
        if result:
            return result
        
        logger.error("所有方法都失败了，无法获取视频信息")
        return None

def main():
    """主函数"""
    test_url = "https://v.douyin.com/5UheTSjzZx0/"
    
    spider = DouyinAdvancedSpider()
    result = spider.parse_video(test_url)
    
    if result:
        print("\n" + "="*60)
        print("🎉 解析成功！视频信息如下：")
        print("="*60)
        for key, value in result.items():
            print(f"📝 {key}: {value}")
        print("="*60)
        
        # 如果有视频URL，尝试验证
        if 'video_url' in result:
            print(f"\n🔗 视频直链: {result['video_url']}")
            print("✅ 可以使用此链接下载视频")
    else:
        print("\n❌ 解析失败，请检查日志获取更多信息")

if __name__ == "__main__":
    main()
