#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微视视频解析器
目标：获取视频的CDN直链、作者、文案、封面等信息
"""

import requests
import re
import json
import time
import logging
from urllib.parse import urlparse, parse_qs, unquote

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WeishiSpider:
    def __init__(self):
        self.session = requests.Session()
        self.setup_headers()
        
    def setup_headers(self):
        """设置请求头"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.139 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="139", "Google Chrome";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Connection': 'keep-alive',
            'Referer': 'https://video.weishi.qq.com/'
        }
        self.session.headers.update(self.headers)

    def get_real_url(self, short_url):
        """获取真实URL"""
        try:
            response = self.session.head(short_url, allow_redirects=True, timeout=15)
            real_url = response.url
            logger.info(f"真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            return short_url

    def extract_video_id(self, url):
        """从URL中提取视频ID"""
        patterns = [
            r'/([a-zA-Z0-9]+)$',
            r'video_id=([a-zA-Z0-9]+)',
            r'id=([a-zA-Z0-9]+)',
            r'/video/([a-zA-Z0-9]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                video_id = match.group(1)
                logger.info(f"提取到视频ID: {video_id}")
                return video_id
        
        logger.warning("未能提取到视频ID")
        return None

    def get_page_content(self, url):
        """获取页面内容"""
        try:
            logger.info(f"获取页面内容: {url}")
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code != 200:
                raise Exception(f"HTTP状态码错误: {response.status_code}")
            
            html_content = response.text
            
            if not html_content or len(html_content) < 100:
                raise Exception("页面内容过短，可能加载失败")
            
            logger.info(f"页面内容获取成功，长度: {len(html_content)}")
            
            # 保存HTML用于调试
            with open(f'weishi_page_{int(time.time())}.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return html_content
            
        except Exception as e:
            logger.error(f"获取页面内容失败: {e}")
            raise

    def try_api_requests(self, video_id, real_url):
        """尝试API请求"""
        if not video_id:
            return None

        # 微视的API端点（基于逆向分析）
        api_endpoints = [
            # 标准API端点
            f"https://h5.qzone.qq.com/webapp/json/weishi_feeds_proxy/getVideoByVid?vid={video_id}",
            f"https://video.weishi.qq.com/webapp/json/weishi_feeds_proxy/getVideoByVid?vid={video_id}",
            f"https://api.weishi.qq.com/trpc.weishi.weishi_h5_proxy.weishi_h5_proxy/GetVideoByVid?vid={video_id}",

            # 新的API端点（基于页面分析）
            f"https://isee.weishi.qq.com/cgi-bin/h5/video/get_video_info?vid={video_id}",
            f"https://api.weishi.qq.com/trpc.weishi.weishi_h5_proxy.weishi_h5_proxy/GetVideoInfo?vid={video_id}",
            f"https://h5.qzone.qq.com/proxy/domain/isee.weishi.qq.com/cgi-bin/h5/video/get_video_info?vid={video_id}",

            # 尝试不同的参数格式
            f"https://api.weishi.qq.com/trpc.weishi.weishi_h5_proxy.weishi_h5_proxy/GetVideoByVid",
        ]
        
        for api_url in api_endpoints:
            try:
                logger.info(f"尝试API: {api_url}")

                headers = self.headers.copy()
                headers['X-Requested-With'] = 'XMLHttpRequest'
                headers['Accept'] = 'application/json, text/plain, */*'
                headers['Origin'] = 'https://isee.weishi.qq.com'
                headers['Referer'] = real_url

                # 对于POST请求的API
                if 'GetVideoByVid' in api_url and not '?' in api_url:
                    # 尝试POST请求
                    post_data = {
                        'vid': video_id,
                        'reqType': 1
                    }
                    response = self.session.post(api_url, json=post_data, headers=headers, timeout=15)
                else:
                    response = self.session.get(api_url, headers=headers, timeout=15)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"API请求成功: {api_url}")

                        # 保存API响应
                        with open(f'weishi_api_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)

                        # 检查响应是否有效
                        if self.is_valid_weishi_response(data):
                            # 解析响应
                            video_info = self.parse_api_response(data)
                            if video_info:
                                return video_info
                        else:
                            logger.debug(f"API响应无效或为空")
                    except json.JSONDecodeError:
                        logger.debug(f"API响应不是JSON格式")

            except Exception as e:
                logger.debug(f"API请求失败: {e}")
        
        return None

    def is_valid_weishi_response(self, data):
        """检查微视API响应是否有效"""
        if not isinstance(data, dict):
            return False

        # 检查常见的有效响应结构
        valid_indicators = [
            'video_url',
            'play_url',
            'mp4_url',
            'feeds',
            'video_info',
            'data'
        ]

        # 检查错误响应
        if 'ret' in data and data['ret'] != 0:
            return False

        if 'rsp_header' in data:
            rsp_header = data['rsp_header']
            if isinstance(rsp_header, dict) and rsp_header.get('ret', 0) != 0:
                return False

        # 检查是否包含视频相关数据
        for indicator in valid_indicators:
            if indicator in data:
                value = data[indicator]
                if isinstance(value, (list, dict)) and value:
                    return True
                elif isinstance(value, str) and value.startswith('http'):
                    return True

        return False

    def deep_parse_html_content(self, html):
        """深度解析HTML内容，寻找所有可能的视频数据"""
        try:
            logger.info("🔍 深度解析HTML内容...")

            # 首先尝试从页面中提取所有可能的视频URL
            video_url_patterns = [
                r'https?://[^"\']*\.mp4[^"\']*',
                r'https?://[^"\']*video[^"\']*\.mp4',
                r'https?://[^"\']*weishi[^"\']*\.mp4',
                r'https?://puui\.qpic\.cn/[^"\']*\.mp4',
                r'https?://[^"\']*\.qq\.com/[^"\']*\.mp4',
                r'"url"\s*:\s*"([^"]*\.mp4[^"]*)"',
                r'"video_url"\s*:\s*"([^"]*)"',
                r'"play_url"\s*:\s*"([^"]*)"',
                r'"src"\s*:\s*"([^"]*\.mp4[^"]*)"',
            ]

            found_videos = []
            for pattern in video_url_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                for match in matches:
                    if match.startswith('http') and '.mp4' in match:
                        found_videos.append(match)
                        logger.info(f"发现视频URL: {match}")

            # 去重
            found_videos = list(set(found_videos))

            if found_videos:
                # 如果找到视频URL，尝试提取其他信息
                title = self.extract_title_from_html(html)
                author = self.extract_author_from_html(html)

                return {
                    'video_url': found_videos[0],  # 使用第一个找到的视频URL
                    'title': title or 'Unknown',
                    'author': author or 'Unknown',
                    'all_video_urls': found_videos
                }

            # 如果没有找到直接的视频URL，尝试解析JavaScript数据
            return self.parse_javascript_data(html)

        except Exception as e:
            logger.error(f"深度HTML解析失败: {e}")
            return None

    def extract_title_from_html(self, html):
        """从HTML中提取标题"""
        title_patterns = [
            r'"title"\s*:\s*"([^"]+)"',
            r'"desc"\s*:\s*"([^"]+)"',
            r'"content"\s*:\s*"([^"]+)"',
            r'<title[^>]*>([^<]+)</title>',
        ]

        for pattern in title_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                title = match.group(1).strip()
                if title and title != 'Unknown' and len(title) > 1:
                    return title

        return None

    def extract_author_from_html(self, html):
        """从HTML中提取作者"""
        author_patterns = [
            r'"nickname"\s*:\s*"([^"]+)"',
            r'"nick"\s*:\s*"([^"]+)"',
            r'"author"\s*:\s*"([^"]+)"',
            r'"username"\s*:\s*"([^"]+)"',
            r'"name"\s*:\s*"([^"]+)"',
        ]

        for pattern in author_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                author = match.group(1).strip()
                if author and author != 'Unknown' and len(author) > 1:
                    return author

        return None

    def parse_javascript_data(self, html):
        """解析JavaScript数据"""
        try:
            # 查找所有可能的JavaScript数据结构
            js_patterns = [
                r'window\.Vise\.initState\s*=\s*({.+?});',
                r'window\.Vise\.userData\s*=\s*({.+?});',
                r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                r'window\.WEISHI_DATA\s*=\s*({.+?});',
                r'window\.videoInfo\s*=\s*({.+?});',
                r'var\s+videoData\s*=\s*({.+?});',
                r'const\s+videoInfo\s*=\s*({.+?});',
            ]

            for pattern in js_patterns:
                match = re.search(pattern, html, re.DOTALL)
                if match:
                    try:
                        data = json.loads(match.group(1))
                        logger.info(f"解析JavaScript数据成功: {pattern[:30]}...")

                        # 递归搜索视频信息
                        video_info = self.extract_video_info_recursive(data)
                        if video_info and len(video_info) >= 2:
                            return video_info
                    except json.JSONDecodeError:
                        continue

            return None

        except Exception as e:
            logger.error(f"JavaScript数据解析失败: {e}")
            return None

    def parse_html_content(self, html):
        """解析HTML内容"""
        try:
            # 首先尝试深度解析
            result = self.deep_parse_html_content(html)
            if result:
                return result

            # 如果深度解析失败，使用原来的方法
            # 微视特有的数据结构
            patterns = [
                (r'window\.Vise\.initState\s*=\s*({.+?});', 'VISE_INIT_STATE'),
                (r'window\.Vise\.userData\s*=\s*({.+?});', 'VISE_USER_DATA'),
                (r'window\.__INITIAL_STATE__\s*=\s*({.+?});', 'INITIAL_STATE'),
                (r'window\.WEISHI_DATA\s*=\s*({.+?});', 'WEISHI_DATA'),
                (r'window\.videoInfo\s*=\s*({.+?});', 'VIDEO_INFO'),
                (r'"video_url"\s*:\s*"([^"]+)"', 'VIDEO_URL_DIRECT'),
                (r'"play_url"\s*:\s*"([^"]+)"', 'PLAY_URL_DIRECT'),
                (r'"mp4_url"\s*:\s*"([^"]+)"', 'MP4_URL_DIRECT'),
                (r'"url"\s*:\s*"([^"]+\.mp4[^"]*)"', 'MP4_URL_PATTERN'),
                (r'"title"\s*:\s*"([^"]+)"', 'TITLE_DIRECT'),
                (r'"nickname"\s*:\s*"([^"]+)"', 'NICKNAME_DIRECT'),
                (r'"nick"\s*:\s*"([^"]+)"', 'NICK_DIRECT'),
            ]
            
            video_info = {}
            
            for pattern, name in patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                for i, match in enumerate(matches):
                    try:
                        if name.endswith('_DIRECT') or name.endswith('_PATTERN'):
                            # 直接提取的字符串
                            if name in ['VIDEO_URL_DIRECT', 'PLAY_URL_DIRECT', 'MP4_URL_DIRECT', 'MP4_URL_PATTERN']:
                                if match.startswith('http'):
                                    video_info['video_url'] = match
                                    logger.info(f"从HTML直接提取到视频URL: {match}")
                            elif name == 'TITLE_DIRECT':
                                video_info['title'] = match
                                logger.info(f"从HTML直接提取到标题: {match}")
                            elif name in ['NICKNAME_DIRECT', 'NICK_DIRECT']:
                                video_info['author'] = match
                                logger.info(f"从HTML直接提取到作者: {match}")
                        else:
                            # JSON数据
                            data = json.loads(match)
                            logger.info(f"从HTML解析到数据: {name}-{i+1}")
                            
                            # 保存调试数据
                            with open(f'weishi_html_{name}_{i+1}_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)
                            
                            # 解析视频信息
                            parsed_info = self.parse_api_response(data)
                            if parsed_info:
                                video_info.update(parsed_info)
                    
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.debug(f"JSON解析失败 ({name}-{i+1}): {e}")
                        continue
            
            if video_info and len(video_info) >= 2:
                return video_info
        
        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
        
        return None

    def parse_api_response(self, data):
        """解析API响应"""
        try:
            video_info = {}
            
            # 递归搜索视频信息
            found_info = self.extract_video_info_recursive(data)
            if found_info and len(found_info) >= 2:
                return found_info
            
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
        
        return None

    def extract_video_info_recursive(self, obj, depth=0, max_depth=15):
        """递归提取视频信息"""
        if depth > max_depth:
            return None
        
        video_info = {}
        
        if isinstance(obj, dict):
            # 查找关键字段
            field_mappings = {
                'title': ['title', 'desc', 'content', 'text', 'summary'],
                'author': ['nickname', 'author', 'username', 'name', 'user_name'],
                'cover': ['cover', 'thumbnail', 'poster', 'cover_url', 'thumb'],
                'video_url': ['video_url', 'play_url', 'mp4_url', 'url', 'src'],
                'video_id': ['video_id', 'vid', 'id', 'item_id'],
                'duration': ['duration', 'video_duration', 'time'],
            }
            
            for info_key, possible_keys in field_mappings.items():
                for key in possible_keys:
                    if key in obj and obj[key]:
                        value = obj[key]
                        if info_key in ['video_url', 'cover']:
                            url = self.extract_url_from_value(value)
                            if url:
                                video_info[info_key] = url
                        else:
                            video_info[info_key] = str(value)
                        break
            
            # 如果找到了足够的信息，返回
            if len(video_info) >= 3:
                return video_info
            
            # 递归搜索
            for key, value in obj.items():
                if isinstance(value, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(value, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        elif isinstance(obj, list):
            for item in obj:
                if isinstance(item, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(item, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        return video_info if len(video_info) >= 2 else None

    def extract_url_from_value(self, value):
        """从值中提取URL"""
        if isinstance(value, str) and value.startswith(('http', '//')):
            return value
        elif isinstance(value, dict):
            url_keys = ['url', 'uri', 'src', 'href', 'video_url', 'play_url']
            for key in url_keys:
                if key in value:
                    url_value = value[key]
                    if isinstance(url_value, str) and url_value.startswith(('http', '//')):
                        return url_value
                    elif isinstance(url_value, list) and url_value:
                        return self.extract_url_from_value(url_value[0])
        elif isinstance(value, list) and value:
            return self.extract_url_from_value(value[0])
        
        return None

    def analyze_javascript_apis(self, html):
        """分析JavaScript中的API端点"""
        try:
            logger.info("🔍 分析JavaScript中的API端点...")

            # 查找可能的API URL模式
            api_patterns = [
                r'https?://[^"\']*api[^"\']*weishi[^"\']*',
                r'https?://[^"\']*weishi[^"\']*api[^"\']*',
                r'https?://[^"\']*isee\.weishi\.qq\.com[^"\']*',
                r'https?://[^"\']*h5\.qzone\.qq\.com[^"\']*',
                r'/cgi-bin/[^"\']*video[^"\']*',
                r'/webapp/json/[^"\']*',
                r'trpc\.[^"\']*weishi[^"\']*',
            ]

            found_apis = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                for match in matches:
                    if match not in found_apis:
                        found_apis.add(match)
                        logger.info(f"发现API端点: {match}")

            return list(found_apis)

        except Exception as e:
            logger.error(f"JavaScript API分析失败: {e}")
            return []

    def try_dynamic_video_loading(self, video_id, real_url):
        """尝试动态视频加载"""
        try:
            logger.info("🎬 尝试动态视频加载...")

            # 模拟页面加载后的AJAX请求
            headers = self.headers.copy()
            headers.update({
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json, text/plain, */*',
                'Origin': 'https://isee.weishi.qq.com',
                'Referer': real_url,
                'Content-Type': 'application/json'
            })

            # 尝试不同的动态加载API
            dynamic_apis = [
                f"https://isee.weishi.qq.com/cgi-bin/h5/feeds/get_feeds?vid={video_id}",
                f"https://isee.weishi.qq.com/cgi-bin/h5/video/get_video_detail?vid={video_id}",
                f"https://h5.qzone.qq.com/proxy/domain/isee.weishi.qq.com/cgi-bin/h5/feeds/get_feeds?vid={video_id}",
            ]

            for api_url in dynamic_apis:
                try:
                    logger.info(f"尝试动态API: {api_url}")
                    response = self.session.get(api_url, headers=headers, timeout=15)

                    if response.status_code == 200:
                        try:
                            data = response.json()
                            logger.info(f"动态API请求成功: {api_url}")

                            # 保存响应
                            with open(f'weishi_dynamic_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)

                            if self.is_valid_weishi_response(data):
                                video_info = self.parse_api_response(data)
                                if video_info:
                                    return video_info
                        except json.JSONDecodeError:
                            pass

                except Exception as e:
                    logger.debug(f"动态API请求失败: {e}")

        except Exception as e:
            logger.error(f"动态视频加载失败: {e}")

        return None

    def parse_video(self, url):
        """解析视频的主方法"""
        logger.info(f"🚀 开始解析微视视频: {url}")

        # 1. 获取真实URL
        real_url = self.get_real_url(url)

        # 2. 提取视频ID
        video_id = self.extract_video_id(real_url)

        # 3. 获取页面内容
        logger.info("📄 获取页面内容...")
        html_content = self.get_page_content(real_url)
        if not html_content:
            logger.error("无法获取页面内容")
            return None

        # 4. 分析JavaScript中的API端点
        found_apis = self.analyze_javascript_apis(html_content)

        # 5. 分析JavaScript文件
        js_apis = self.analyze_js_files(html_content)

        # 6. 尝试标准API请求
        logger.info("🔍 尝试标准API请求...")
        result = self.try_api_requests(video_id, real_url)
        if result:
            return result

        # 7. 尝试动态视频加载
        result = self.try_dynamic_video_loading(video_id, real_url)
        if result:
            return result

        # 8. 尝试逆向工程方法
        result = self.try_reverse_engineering_approach(video_id, real_url)
        if result:
            return result

        # 9. 尝试获取用户视频列表
        logger.info("👤 尝试获取用户视频列表...")
        result = self.try_user_feeds_api(video_id)
        if result:
            return result

        # 10. 解析HTML内容
        logger.info("📄 解析HTML内容...")
        result = self.parse_html_content(html_content)
        if result:
            return result

        logger.error("❌ 所有解析方法都失败了")
        return None

    def analyze_js_files(self, html):
        """分析JavaScript文件中的API端点"""
        try:
            logger.info("🔍 分析JavaScript文件...")

            # 提取JS文件URL
            js_patterns = [
                r'src=["\']([^"\']*\.js[^"\']*)["\']',
                r'href=["\']([^"\']*\.js[^"\']*)["\']',
            ]

            js_files = set()
            for pattern in js_patterns:
                matches = re.findall(pattern, html)
                for match in matches:
                    if 'isee.weishi.qq.com' in match:
                        js_files.add(match)

            # 分析每个JS文件
            for js_url in js_files:
                try:
                    logger.info(f"分析JS文件: {js_url}")
                    response = self.session.get(js_url, timeout=10)

                    if response.status_code == 200:
                        js_content = response.text

                        # 在JS中寻找API端点
                        api_patterns = [
                            r'["\']([^"\']*cgi-bin[^"\']*video[^"\']*)["\']',
                            r'["\']([^"\']*api[^"\']*weishi[^"\']*)["\']',
                            r'["\']([^"\']*feeds[^"\']*get[^"\']*)["\']',
                            r'["\']([^"\']*video[^"\']*detail[^"\']*)["\']',
                        ]

                        found_apis = []
                        for pattern in api_patterns:
                            matches = re.findall(pattern, js_content)
                            for match in matches:
                                if match.startswith('/') or match.startswith('http'):
                                    found_apis.append(match)
                                    logger.info(f"在JS中发现API: {match}")

                        if found_apis:
                            return found_apis

                except Exception as e:
                    logger.debug(f"分析JS文件失败: {e}")

        except Exception as e:
            logger.error(f"JS文件分析失败: {e}")

        return []

    def try_reverse_engineering_approach(self, video_id, real_url):
        """尝试逆向工程方法"""
        try:
            logger.info("🔧 尝试逆向工程方法...")

            # 方法1: 尝试构造微视的内部API
            internal_apis = [
                f"https://isee.weishi.qq.com/cgi-bin/microvision-bin/getfeed?feedid={video_id}",
                f"https://isee.weishi.qq.com/cgi-bin/microvision-bin/getvideo?vid={video_id}",
                f"https://h5.qzone.qq.com/proxy/domain/isee.weishi.qq.com/cgi-bin/microvision-bin/getfeed?feedid={video_id}",
            ]

            for api_url in internal_apis:
                try:
                    logger.info(f"尝试内部API: {api_url}")

                    headers = self.headers.copy()
                    headers.update({
                        'Accept': 'application/json, text/plain, */*',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Origin': 'https://isee.weishi.qq.com',
                        'Referer': real_url
                    })

                    response = self.session.get(api_url, headers=headers, timeout=15)

                    if response.status_code == 200:
                        try:
                            data = response.json()
                            logger.info(f"内部API请求成功: {api_url}")

                            # 保存响应
                            with open(f'weishi_internal_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)

                            if self.is_valid_weishi_response(data):
                                video_info = self.parse_api_response(data)
                                if video_info:
                                    return video_info
                        except json.JSONDecodeError:
                            # 可能是其他格式的响应
                            logger.info(f"非JSON响应，长度: {len(response.text)}")

                except Exception as e:
                    logger.debug(f"内部API请求失败: {e}")

            # 方法2: 尝试模拟移动端APP的请求
            mobile_apis = [
                f"https://api.weishi.qq.com/trpc.weishi.weishi_h5_proxy.weishi_h5_proxy/GetVideoByVid",
            ]

            for api_url in mobile_apis:
                try:
                    logger.info(f"尝试移动端API: {api_url}")

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Origin': 'https://isee.weishi.qq.com',
                        'Referer': real_url
                    }

                    # 构造请求体
                    request_data = {
                        "vid": video_id,
                        "reqType": 1,
                        "from": "h5"
                    }

                    response = self.session.post(api_url, json=request_data, headers=headers, timeout=15)

                    if response.status_code == 200:
                        try:
                            data = response.json()
                            logger.info(f"移动端API请求成功: {api_url}")

                            # 保存响应
                            with open(f'weishi_mobile_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)

                            if self.is_valid_weishi_response(data):
                                video_info = self.parse_api_response(data)
                                if video_info:
                                    return video_info
                        except json.JSONDecodeError:
                            pass

                except Exception as e:
                    logger.debug(f"移动端API请求失败: {e}")

        except Exception as e:
            logger.error(f"逆向工程方法失败: {e}")

        return None

    def try_user_feeds_api(self, video_id, user_id=None):
        """尝试获取用户的视频列表"""
        try:
            logger.info("🎬 尝试获取用户视频列表...")

            if not user_id:
                # 从之前的数据中提取用户ID
                user_id = "1755431320152719"  # 从VISE_INIT_STATE中获取的用户ID

            # 构造获取用户视频列表的API
            feeds_apis = [
                f"https://isee.weishi.qq.com/cgi-bin/h5/feeds/get_feeds?person_id={user_id}&cursor=0&count=20",
                f"https://api.weishi.qq.com/trpc.weishi.weishi_h5_proxy.weishi_h5_proxy/GetPersonFeeds?person_id={user_id}",
                f"https://h5.qzone.qq.com/proxy/domain/isee.weishi.qq.com/cgi-bin/h5/feeds/get_feeds?person_id={user_id}&cursor=0&count=20",
            ]

            for api_url in feeds_apis:
                try:
                    logger.info(f"尝试用户视频API: {api_url}")

                    headers = self.headers.copy()
                    headers.update({
                        'Accept': 'application/json, text/plain, */*',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Origin': 'https://isee.weishi.qq.com',
                        'Referer': 'https://isee.weishi.qq.com/'
                    })

                    response = self.session.get(api_url, headers=headers, timeout=15)

                    if response.status_code == 200:
                        try:
                            data = response.json()
                            logger.info(f"用户视频API请求成功: {api_url}")

                            # 保存响应
                            with open(f'weishi_user_feeds_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)

                            # 在视频列表中查找目标视频
                            target_video = self.find_target_video_in_feeds(data, video_id)
                            if target_video:
                                return target_video

                        except json.JSONDecodeError:
                            pass

                except Exception as e:
                    logger.debug(f"用户视频API请求失败: {e}")

        except Exception as e:
            logger.error(f"获取用户视频列表失败: {e}")

        return None

    def find_target_video_in_feeds(self, feeds_data, target_video_id):
        """在视频列表中查找目标视频"""
        try:
            # 递归搜索视频列表
            def search_feeds(obj, depth=0):
                if depth > 10:
                    return None

                if isinstance(obj, dict):
                    # 检查是否是目标视频
                    video_id = obj.get('id') or obj.get('video_id') or obj.get('feed_id')
                    if video_id == target_video_id:
                        logger.info(f"找到目标视频: {target_video_id}")
                        return self.extract_video_info_recursive(obj)

                    # 递归搜索
                    for key, value in obj.items():
                        if isinstance(value, (dict, list)):
                            result = search_feeds(value, depth + 1)
                            if result:
                                return result

                elif isinstance(obj, list):
                    for item in obj:
                        if isinstance(item, (dict, list)):
                            result = search_feeds(item, depth + 1)
                            if result:
                                return result

                return None

            return search_feeds(feeds_data)

        except Exception as e:
            logger.error(f"在视频列表中查找目标视频失败: {e}")
            return None

def main():
    """主函数"""
    test_url = "https://video.weishi.qq.com/qvW4Kldp"
    
    print("🎬 微视视频解析器")
    print("=" * 60)
    
    spider = WeishiSpider()
    result = spider.parse_video(test_url)
    
    if result:
        print("\n🎉 解析成功！视频信息如下：")
        print("=" * 60)
        for key, value in result.items():
            if key == 'video_url':
                print(f"🎥 视频链接: {value}")
            elif key == 'title':
                print(f"📝 标题: {value}")
            elif key == 'author':
                print(f"👤 作者: {value}")
            elif key == 'cover':
                print(f"🖼️  封面: {value}")
            else:
                print(f"📋 {key}: {value}")
        print("=" * 60)
        
        if 'video_url' in result:
            print(f"\n✅ 视频直链获取成功！")
            print(f"🔗 下载链接: {result['video_url']}")
            print("💡 可以直接使用此链接下载视频")
    else:
        print("\n❌ 解析失败")
        print("💡 建议检查生成的调试文件获取更多信息")

if __name__ == "__main__":
    main()
