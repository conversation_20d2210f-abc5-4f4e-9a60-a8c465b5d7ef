#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微视视频解析器
目标：获取视频的CDN直链、作者、文案、封面等信息
"""

import requests
import re
import json
import time
import logging
from urllib.parse import urlparse, parse_qs, unquote

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WeishiSpider:
    def __init__(self):
        self.session = requests.Session()
        self.setup_headers()
        
    def setup_headers(self):
        """设置请求头"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.7258.139 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="139", "Google Chrome";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Connection': 'keep-alive',
            'Referer': 'https://video.weishi.qq.com/'
        }
        self.session.headers.update(self.headers)

    def get_real_url(self, short_url):
        """获取真实URL"""
        try:
            response = self.session.head(short_url, allow_redirects=True, timeout=15)
            real_url = response.url
            logger.info(f"真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            return short_url

    def extract_video_id(self, url):
        """从URL中提取视频ID"""
        patterns = [
            r'/([a-zA-Z0-9]+)$',
            r'video_id=([a-zA-Z0-9]+)',
            r'id=([a-zA-Z0-9]+)',
            r'/video/([a-zA-Z0-9]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                video_id = match.group(1)
                logger.info(f"提取到视频ID: {video_id}")
                return video_id
        
        logger.warning("未能提取到视频ID")
        return None

    def get_page_content(self, url):
        """获取页面内容"""
        try:
            logger.info(f"获取页面内容: {url}")
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code != 200:
                raise Exception(f"HTTP状态码错误: {response.status_code}")
            
            html_content = response.text
            
            if not html_content or len(html_content) < 100:
                raise Exception("页面内容过短，可能加载失败")
            
            logger.info(f"页面内容获取成功，长度: {len(html_content)}")
            
            # 保存HTML用于调试
            with open(f'weishi_page_{int(time.time())}.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return html_content
            
        except Exception as e:
            logger.error(f"获取页面内容失败: {e}")
            raise

    def try_api_requests(self, video_id, real_url):
        """尝试API请求"""
        if not video_id:
            return None
        
        # 微视可能的API端点
        api_endpoints = [
            f"https://h5.qzone.qq.com/webapp/json/weishi_feeds_proxy/getVideoByVid?vid={video_id}",
            f"https://video.weishi.qq.com/webapp/json/weishi_feeds_proxy/getVideoByVid?vid={video_id}",
            f"https://api.weishi.qq.com/trpc.weishi.weishi_h5_proxy.weishi_h5_proxy/GetVideoByVid?vid={video_id}",
        ]
        
        for api_url in api_endpoints:
            try:
                logger.info(f"尝试API: {api_url}")
                
                headers = self.headers.copy()
                headers['X-Requested-With'] = 'XMLHttpRequest'
                headers['Accept'] = 'application/json, text/plain, */*'
                
                response = self.session.get(api_url, headers=headers, timeout=15)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"API请求成功: {api_url}")
                        
                        # 保存API响应
                        with open(f'weishi_api_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        # 解析响应
                        video_info = self.parse_api_response(data)
                        if video_info:
                            return video_info
                    except json.JSONDecodeError:
                        logger.debug(f"API响应不是JSON格式")
                        
            except Exception as e:
                logger.debug(f"API请求失败: {e}")
        
        return None

    def parse_html_content(self, html):
        """解析HTML内容"""
        try:
            # 查找各种可能的数据结构
            patterns = [
                (r'window\.__INITIAL_STATE__\s*=\s*({.+?});', 'INITIAL_STATE'),
                (r'window\.WEISHI_DATA\s*=\s*({.+?});', 'WEISHI_DATA'),
                (r'window\.videoInfo\s*=\s*({.+?});', 'VIDEO_INFO'),
                (r'"video_url"\s*:\s*"([^"]+)"', 'VIDEO_URL_DIRECT'),
                (r'"play_url"\s*:\s*"([^"]+)"', 'PLAY_URL_DIRECT'),
                (r'"mp4_url"\s*:\s*"([^"]+)"', 'MP4_URL_DIRECT'),
                (r'"title"\s*:\s*"([^"]+)"', 'TITLE_DIRECT'),
                (r'"nickname"\s*:\s*"([^"]+)"', 'NICKNAME_DIRECT'),
            ]
            
            video_info = {}
            
            for pattern, name in patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                for i, match in enumerate(matches):
                    try:
                        if name.endswith('_DIRECT'):
                            # 直接提取的字符串
                            if name == 'VIDEO_URL_DIRECT' or name == 'PLAY_URL_DIRECT' or name == 'MP4_URL_DIRECT':
                                if match.startswith('http'):
                                    video_info['video_url'] = match
                                    logger.info(f"从HTML直接提取到视频URL: {match}")
                            elif name == 'TITLE_DIRECT':
                                video_info['title'] = match
                                logger.info(f"从HTML直接提取到标题: {match}")
                            elif name == 'NICKNAME_DIRECT':
                                video_info['author'] = match
                                logger.info(f"从HTML直接提取到作者: {match}")
                        else:
                            # JSON数据
                            data = json.loads(match)
                            logger.info(f"从HTML解析到数据: {name}-{i+1}")
                            
                            # 保存调试数据
                            with open(f'weishi_html_{name}_{i+1}_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)
                            
                            # 解析视频信息
                            parsed_info = self.parse_api_response(data)
                            if parsed_info:
                                video_info.update(parsed_info)
                    
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.debug(f"JSON解析失败 ({name}-{i+1}): {e}")
                        continue
            
            if video_info and len(video_info) >= 2:
                return video_info
        
        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
        
        return None

    def parse_api_response(self, data):
        """解析API响应"""
        try:
            video_info = {}
            
            # 递归搜索视频信息
            found_info = self.extract_video_info_recursive(data)
            if found_info and len(found_info) >= 2:
                return found_info
            
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
        
        return None

    def extract_video_info_recursive(self, obj, depth=0, max_depth=15):
        """递归提取视频信息"""
        if depth > max_depth:
            return None
        
        video_info = {}
        
        if isinstance(obj, dict):
            # 查找关键字段
            field_mappings = {
                'title': ['title', 'desc', 'content', 'text', 'summary'],
                'author': ['nickname', 'author', 'username', 'name', 'user_name'],
                'cover': ['cover', 'thumbnail', 'poster', 'cover_url', 'thumb'],
                'video_url': ['video_url', 'play_url', 'mp4_url', 'url', 'src'],
                'video_id': ['video_id', 'vid', 'id', 'item_id'],
                'duration': ['duration', 'video_duration', 'time'],
            }
            
            for info_key, possible_keys in field_mappings.items():
                for key in possible_keys:
                    if key in obj and obj[key]:
                        value = obj[key]
                        if info_key in ['video_url', 'cover']:
                            url = self.extract_url_from_value(value)
                            if url:
                                video_info[info_key] = url
                        else:
                            video_info[info_key] = str(value)
                        break
            
            # 如果找到了足够的信息，返回
            if len(video_info) >= 3:
                return video_info
            
            # 递归搜索
            for key, value in obj.items():
                if isinstance(value, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(value, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        elif isinstance(obj, list):
            for item in obj:
                if isinstance(item, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(item, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        return video_info if len(video_info) >= 2 else None

    def extract_url_from_value(self, value):
        """从值中提取URL"""
        if isinstance(value, str) and value.startswith(('http', '//')):
            return value
        elif isinstance(value, dict):
            url_keys = ['url', 'uri', 'src', 'href', 'video_url', 'play_url']
            for key in url_keys:
                if key in value:
                    url_value = value[key]
                    if isinstance(url_value, str) and url_value.startswith(('http', '//')):
                        return url_value
                    elif isinstance(url_value, list) and url_value:
                        return self.extract_url_from_value(url_value[0])
        elif isinstance(value, list) and value:
            return self.extract_url_from_value(value[0])
        
        return None

    def parse_video(self, url):
        """解析视频的主方法"""
        logger.info(f"🚀 开始解析微视视频: {url}")
        
        # 1. 获取真实URL
        real_url = self.get_real_url(url)
        
        # 2. 提取视频ID
        video_id = self.extract_video_id(real_url)
        
        # 3. 尝试API请求
        logger.info("🔍 尝试API请求...")
        result = self.try_api_requests(video_id, real_url)
        if result:
            return result
        
        # 4. 获取并解析页面内容
        logger.info("📄 获取并解析页面内容...")
        html_content = self.get_page_content(real_url)
        if html_content:
            result = self.parse_html_content(html_content)
            if result:
                return result
        
        logger.error("❌ 所有解析方法都失败了")
        return None

def main():
    """主函数"""
    test_url = "https://video.weishi.qq.com/qvW4Kldp"
    
    print("🎬 微视视频解析器")
    print("=" * 60)
    
    spider = WeishiSpider()
    result = spider.parse_video(test_url)
    
    if result:
        print("\n🎉 解析成功！视频信息如下：")
        print("=" * 60)
        for key, value in result.items():
            if key == 'video_url':
                print(f"🎥 视频链接: {value}")
            elif key == 'title':
                print(f"📝 标题: {value}")
            elif key == 'author':
                print(f"👤 作者: {value}")
            elif key == 'cover':
                print(f"🖼️  封面: {value}")
            else:
                print(f"📋 {key}: {value}")
        print("=" * 60)
        
        if 'video_url' in result:
            print(f"\n✅ 视频直链获取成功！")
            print(f"🔗 下载链接: {result['video_url']}")
            print("💡 可以直接使用此链接下载视频")
    else:
        print("\n❌ 解析失败")
        print("💡 建议检查生成的调试文件获取更多信息")

if __name__ == "__main__":
    main()
