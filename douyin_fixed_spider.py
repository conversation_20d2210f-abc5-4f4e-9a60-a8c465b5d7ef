#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频解析器 - 基于成功云函数的Python实现
参考云函数的成功经验重新实现
"""

import requests
import re
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DouyinFixedSpider:
    def __init__(self):
        self.session = requests.Session()
        self.setup_headers()
        
    def setup_headers(self):
        """设置正确的移动端请求头 - 基于云函数成功经验"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Referer': 'https://www.douyin.com/',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        }
        self.session.headers.update(self.headers)

    def get_real_url(self, short_url):
        """获取真实URL - 模仿云函数的重定向处理"""
        try:
            response = self.session.head(short_url, allow_redirects=False, timeout=8)
            location = response.headers.get('location') or response.headers.get('Location')
            real_url = location or short_url
            logger.info(f"真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            return short_url

    def get_page_content(self, url):
        """获取页面内容 - 使用云函数相同的策略"""
        try:
            logger.info(f"获取页面内容: {url}")
            
            response = self.session.get(url, timeout=10)
            
            if response.status_code != 200:
                raise Exception(f"HTTP状态码错误: {response.status_code}")
            
            html_content = response.text
            
            if not html_content or len(html_content) < 100:
                raise Exception("页面内容过短，可能加载失败")
            
            logger.info(f"页面内容获取成功，长度: {len(html_content)}")
            return html_content
            
        except Exception as e:
            logger.error(f"获取页面内容失败: {e}")
            raise

    def find_field_in_object(self, obj, target_field, alternative_fields=None, depth=0):
        """递归查找对象中的字段 - 复制云函数逻辑"""
        if alternative_fields is None:
            alternative_fields = []
            
        if not obj or not isinstance(obj, dict) or depth > 8:
            return None
        
        # 检查目标字段
        if target_field in obj and isinstance(obj[target_field], str) and obj[target_field].strip():
            return obj[target_field]
        
        # 检查备选字段
        for field in alternative_fields:
            if field in obj and isinstance(obj[field], str) and obj[field].strip():
                return obj[field]
        
        # 递归查找
        for key, value in obj.items():
            if isinstance(value, dict):
                result = self.find_field_in_object(value, target_field, alternative_fields, depth + 1)
                if result:
                    return result
            elif isinstance(value, list):
                for i, item in enumerate(value[:3]):  # 限制数组搜索深度
                    if isinstance(item, dict):
                        result = self.find_field_in_object(item, target_field, alternative_fields, depth + 1)
                        if result:
                            return result
        
        return None

    def extract_title_and_author(self, html):
        """提取标题和作者 - 复制云函数的精确逻辑"""
        # 优先从JSON提取
        router_match = re.search(r'window\._ROUTER_DATA\s*=\s*({.+?})\s*</script>', html, re.DOTALL)
        if router_match:
            try:
                json_data = json.loads(router_match.group(1))
                
                # 打印调试信息
                logger.info("🎬 ===== 从HTML提取的抖音原始JSON数据 =====")
                logger.info(f"📦 完整ROUTER_DATA JSON: {json.dumps(json_data, ensure_ascii=False, indent=2)}")
                logger.info("========================")
                
                title = self.find_field_in_object(json_data, 'desc', ['title']) or ''
                author = self.find_field_in_object(json_data, 'nickname', ['author']) or ''
                
                if title or author:
                    return {'title': title, 'author': author}
                    
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
        
        # 备用方案：从HTML title标签提取标题
        title = ''
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html, re.IGNORECASE)
        if title_match:
            title = re.sub(r'\s*-\s*抖音$', '', title_match.group(1)).strip()
        
        # 备用方案：从正则匹配提取作者
        author = ''
        author_match = re.search(r'"nickname":"([^"]+)"', html)
        if author_match:
            author = author_match.group(1)
        
        return {'title': title, 'author': author}

    def extract_video_info(self, html):
        """提取视频信息 - 使用云函数的精确正则表达式"""
        try:
            if not isinstance(html, str) or len(html) < 100:
                raise Exception("HTML内容无效")
            
            # 提取标题和作者
            title_author = self.extract_title_and_author(html)
            title = title_author['title']
            author = title_author['author']
            
            # 视频URL提取 - 使用云函数的精确模式
            video_url = ''
            
            logger.info("🎯 开始从JSON中提取视频URL...")
            
            video_patterns = [
                r'"play_addr_h264"[^}]+?"url_list":\s*\[\s*"([^"]+)"',  # 无水印
                r'"play_addr"[^}]+?"url_list":\s*\[\s*"([^"]+)"',       # 标准
                r'"video"[^}]*?"play_addr"[^}]+?"url_list":\s*\[\s*"([^"]+)"'  # 图文视频
            ]
            
            pattern_names = ['无水印(play_addr_h264)', '标准(play_addr)', '图文视频']
            
            for i, pattern in enumerate(video_patterns):
                match = re.search(pattern, html)
                logger.info(f"🔍 尝试模式 {i + 1} [{pattern_names[i]}]: {'找到' if match else '未找到'}")
                if match:
                    video_url = match.group(1).replace('\\u002F', '/').replace('\\/', '/')
                    logger.info(f"✅ 使用模式 {i + 1} 提取到视频URL: {video_url}")
                    break
            
            if not video_url:
                raise Exception("未找到视频播放地址")
            
            # 封面提取
            cover_url = ''
            cover_match = re.search(r'"cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"', html)
            if cover_match:
                cover_url = self.fix_incomplete_image_url(cover_match.group(1))
            
            # 提取时长
            duration = 0
            duration_match = re.search(r'"duration":(\d+)', html)
            if duration_match:
                duration = int(duration_match.group(1))
            
            return {
                'title': title,
                'author': author,
                'video_url': video_url,
                'cover_url': cover_url,
                'duration': duration
            }
            
        except Exception as e:
            logger.error(f"提取视频信息失败: {e}")
            raise

    def fix_incomplete_image_url(self, url):
        """修复URL格式 - 复制云函数逻辑"""
        if not url:
            return url
        
        # 处理转义字符
        url = url.replace('\\u002F', '/').replace('\\/', '/')
        
        # 完整URL直接返回
        if url.startswith('http'):
            return url
        
        # 补全协议
        if url.startswith('//'):
            return 'https:' + url
        
        # 相对路径添加域名
        return 'https://p3-sign.douyinpic.com/' + url

    def get_no_watermark_video(self, original_url):
        """去水印处理 - 复制云函数的核心逻辑"""
        try:
            logger.info(f"开始去水印处理: {original_url}")
            
            # 核心去水印方法：将 playwm 替换为 play
            if '/playwm/' in original_url:
                clean_url = original_url.replace('/playwm/', '/play/')
                logger.info(f"去水印成功: {clean_url}")
                return clean_url
            
            # 如果没有 playwm，直接返回原URL
            logger.info("URL中没有水印标识，返回原URL")
            return original_url
            
        except Exception as e:
            logger.error(f"去水印处理失败: {e}")
            return original_url

    def parse_video(self, share_url):
        """解析视频的主方法 - 完全模仿云函数流程"""
        try:
            logger.info(f"开始解析抖音链接: {share_url}")
            
            # 第一步：获取真实URL
            real_url = self.get_real_url(share_url)
            
            # 第二步：获取页面内容
            page_content = self.get_page_content(real_url)
            
            # 第三步：提取视频信息
            video_info = self.extract_video_info(page_content)
            
            # 第四步：去水印处理
            final_video_url = self.get_no_watermark_video(video_info['video_url'])
            
            # 第五步：构造返回结果
            duration_in_seconds = round(video_info['duration'] / 1000) if video_info['duration'] else 0
            is_direct_video = '.mp4' in final_video_url or 'douyinvod.com' in final_video_url
            
            result = {
                'title': video_info['title'],
                'author': video_info['author'],
                'video_url': final_video_url,
                'cover_url': video_info['cover_url'],
                'duration': duration_in_seconds,
                'is_direct_url': is_direct_video,
                'type': 'video',
                'platform': 'douyin',
                'source': '抖音',
                'note': '已获取无水印直链' if is_direct_video else '已获取API链接',
                'original_url': share_url
            }
            
            logger.info("解析成功！")
            return result
            
        except Exception as e:
            logger.error(f"抖音解析失败: {e}")
            raise

def main():
    """主函数"""
    test_url = "https://v.douyin.com/5UheTSjzZx0/"
    
    print("🎬 抖音视频解析器 - 基于云函数成功经验的Python实现")
    print("=" * 70)
    
    spider = DouyinFixedSpider()
    
    try:
        result = spider.parse_video(test_url)
        
        print("\n🎉 解析成功！视频信息如下：")
        print("=" * 70)
        print(f"📝 标题: {result['title']}")
        print(f"👤 作者: {result['author']}")
        print(f"🎥 视频链接: {result['video_url']}")
        print(f"🖼️  封面: {result['cover_url']}")
        print(f"⏱️  时长: {result['duration']}秒")
        print(f"🔗 直链: {'是' if result['is_direct_url'] else '否'}")
        print(f"💡 说明: {result['note']}")
        print("=" * 70)
        
        if result['video_url']:
            print(f"\n✅ 视频直链获取成功！")
            print(f"🔗 下载链接: {result['video_url']}")
            print("💡 可以直接使用此链接下载视频")
        
    except Exception as e:
        print(f"\n❌ 解析失败: {e}")
        print("💡 请检查网络连接或链接是否有效")

if __name__ == "__main__":
    main()
