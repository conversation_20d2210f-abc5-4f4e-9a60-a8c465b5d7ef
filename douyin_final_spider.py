#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频解析器 - 最终版本
使用专业的反爬虫技术和多种解析方法
"""

import requests
import re
import json
import time
import random
import gzip
import base64
import hashlib
from urllib.parse import urlparse, parse_qs, unquote, quote
from fake_useragent import UserAgent
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DouyinFinalSpider:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        # 设置更真实的请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="139", "Google Chrome";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)

    def get_real_url(self, short_url):
        """获取短链接的真实URL"""
        try:
            response = self.session.head(short_url, allow_redirects=True, timeout=15)
            real_url = response.url
            logger.info(f"真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            return short_url

    def extract_video_id(self, url):
        """从URL中提取视频ID"""
        patterns = [
            r'/video/(\d+)',
            r'modal_id=(\d+)',
            r'aweme_id=(\d+)',
            r'/share/video/(\d+)',
            r'/share/jx-video/(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                video_id = match.group(1)
                logger.info(f"提取到视频ID: {video_id}")
                return video_id
        
        logger.warning("未能提取到视频ID")
        return None

    def get_page_content(self, url):
        """获取页面内容并正确解压缩"""
        try:
            headers = self.headers.copy()
            headers['Referer'] = 'https://www.douyin.com/'
            
            response = self.session.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                # 检查内容编码
                content_encoding = response.headers.get('content-encoding', '').lower()
                
                if content_encoding == 'gzip':
                    try:
                        content = gzip.decompress(response.content).decode('utf-8')
                        logger.info(f"成功解压缩gzip内容，长度: {len(content)}")
                    except:
                        content = response.text
                        logger.info(f"gzip解压缩失败，使用原始内容，长度: {len(content)}")
                elif content_encoding == 'br':
                    try:
                        import brotli
                        content = brotli.decompress(response.content).decode('utf-8')
                        logger.info(f"成功解压缩brotli内容，长度: {len(content)}")
                    except:
                        content = response.text
                        logger.info(f"brotli解压缩失败，使用原始内容，长度: {len(content)}")
                else:
                    content = response.text
                    logger.info(f"无压缩内容，长度: {len(content)}")
                
                # 保存解压缩后的内容
                with open(f'douyin_decompressed_{int(time.time())}.html', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                return content
            else:
                logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取页面内容失败: {e}")
            return None

    def try_direct_api_with_signature(self, video_id):
        """尝试带签名的直接API请求"""
        if not video_id:
            return None
        
        try:
            # 生成时间戳
            timestamp = int(time.time())
            
            # 尝试不同的API端点和参数组合
            api_configs = [
                {
                    'url': 'https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/',
                    'params': {
                        'item_ids': video_id,
                        'dytk': '',
                        'aid': '1128',
                        'app_name': 'douyin_web',
                        'device_platform': 'webapp',
                        'channel': 'channel_pc_web',
                        'pc_client_type': '1',
                        'version_code': '170400',
                        'version_name': '17.4.0',
                        'cookie_enabled': 'true',
                        'screen_width': '1920',
                        'screen_height': '1080',
                        'browser_language': 'zh-CN',
                        'browser_platform': 'MacIntel',
                        'browser_name': 'Chrome',
                        'browser_version': '*********',
                        'browser_online': 'true',
                        'engine_name': 'Blink',
                        'engine_version': '*********',
                        'os_name': 'Mac',
                        'os_version': '10.15.7',
                        'cpu_core_num': '8',
                        'device_memory': '8',
                        'platform': 'PC',
                        'downlink': '10',
                        'effective_type': '4g',
                        'round_trip_time': '50',
                        'webid': '7' + str(random.randint(100000000000000000, 999999999999999999)),
                        'msToken': self.generate_ms_token(),
                        'X-Bogus': self.generate_x_bogus(video_id, timestamp),
                        '_signature': self.generate_signature(video_id, timestamp),
                    }
                },
                {
                    'url': 'https://www.douyin.com/aweme/v1/web/aweme/detail/',
                    'params': {
                        'aweme_id': video_id,
                        'aid': '6383',
                        'app_name': 'douyin_web',
                        'device_platform': 'webapp',
                        'pc_client_type': '1',
                        'version_code': '170400',
                        'version_name': '17.4.0',
                        'channel': 'channel_pc_web',
                        'update_version_code': '170400',
                        'webid': '7' + str(random.randint(100000000000000000, 999999999999999999)),
                        'msToken': self.generate_ms_token(),
                        'X-Bogus': self.generate_x_bogus(video_id, timestamp),
                        '_signature': self.generate_signature(video_id, timestamp),
                    }
                }
            ]
            
            for config in api_configs:
                try:
                    logger.info(f"尝试API: {config['url']}")
                    
                    headers = self.headers.copy()
                    headers['Referer'] = 'https://www.douyin.com/'
                    headers['X-Requested-With'] = 'XMLHttpRequest'
                    
                    response = self.session.get(config['url'], params=config['params'], headers=headers, timeout=15)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            logger.info(f"API请求成功: {config['url']}")
                            
                            # 保存API响应
                            with open(f'douyin_api_response_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)
                            
                            # 解析响应
                            video_info = self.parse_api_response(data)
                            if video_info:
                                return video_info
                        except json.JSONDecodeError:
                            logger.debug(f"API响应不是JSON格式")
                    else:
                        logger.debug(f"API请求失败，状态码: {response.status_code}")
                        
                except Exception as e:
                    logger.debug(f"API请求异常: {e}")
            
        except Exception as e:
            logger.error(f"带签名的API请求失败: {e}")
        
        return None

    def generate_ms_token(self):
        """生成msToken"""
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
        return ''.join(random.choice(chars) for _ in range(107))

    def generate_x_bogus(self, video_id, timestamp):
        """生成X-Bogus参数（简化版本）"""
        # 这是一个简化的实现，真实的X-Bogus生成更复杂
        base_string = f"{video_id}{timestamp}"
        hash_obj = hashlib.md5(base_string.encode())
        return hash_obj.hexdigest()[:32]

    def generate_signature(self, video_id, timestamp):
        """生成签名参数（简化版本）"""
        # 这是一个简化的实现，真实的签名生成更复杂
        base_string = f"aweme_id={video_id}&timestamp={timestamp}"
        hash_obj = hashlib.sha256(base_string.encode())
        return hash_obj.hexdigest()[:40]

    def parse_html_content(self, html):
        """解析HTML内容"""
        try:
            # 查找各种可能的数据结构
            patterns = [
                (r'window\.__INITIAL_STATE__\s*=\s*({.+?});', 'INITIAL_STATE'),
                (r'window\._ROUTER_DATA\s*=\s*({.+?});', 'ROUTER_DATA'),
                (r'window\.RENDER_DATA\s*=\s*"([^"]+)"', 'RENDER_DATA_ENCODED'),
                (r'window\.RENDER_DATA\s*=\s*({.+?});', 'RENDER_DATA'),
                (r'window\.SIGI_STATE\s*=\s*({.+?});', 'SIGI_STATE'),
                (r'self\.__pace_f\.push\(\[1,"([^"]+)"\]\)', 'PACE_DATA'),
                (r'<script[^>]*id="RENDER_DATA"[^>]*>([^<]+)</script>', 'RENDER_SCRIPT'),
                (r'"videoObject"\s*:\s*({.+?})', 'VIDEO_OBJECT'),
                (r'"aweme_detail"\s*:\s*({.+?})', 'AWEME_DETAIL'),
            ]
            
            for pattern, name in patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                for i, match in enumerate(matches):
                    try:
                        if name == 'RENDER_DATA_ENCODED' or name == 'PACE_DATA':
                            # 尝试解码
                            decoded_data = self.decode_data(match)
                            if decoded_data:
                                data = json.loads(decoded_data)
                            else:
                                continue
                        else:
                            data = json.loads(match)
                        
                        logger.info(f"从HTML解析到数据: {name}-{i+1}")
                        
                        # 保存调试数据
                        with open(f'douyin_html_{name}_{i+1}_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        # 解析视频信息
                        video_info = self.parse_api_response(data)
                        if video_info:
                            return video_info
                    
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.debug(f"JSON解析失败 ({name}-{i+1}): {e}")
                        continue
        
        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
        
        return None

    def decode_data(self, encoded_data):
        """解码数据"""
        try:
            # 方法1: URL解码
            try:
                decoded = unquote(encoded_data)
                if decoded != encoded_data and self.is_valid_json(decoded):
                    return decoded
            except:
                pass
            
            # 方法2: Base64解码
            try:
                decoded = base64.b64decode(encoded_data).decode('utf-8')
                if self.is_valid_json(decoded):
                    return decoded
            except:
                pass
            
            # 方法3: 尝试其他编码
            try:
                # 可能是其他编码格式
                decoded = encoded_data.encode('latin1').decode('utf-8')
                if self.is_valid_json(decoded):
                    return decoded
            except:
                pass
                
        except Exception as e:
            logger.debug(f"解码失败: {e}")
        
        return None

    def is_valid_json(self, text):
        """检查是否是有效的JSON"""
        try:
            json.loads(text)
            return True
        except:
            return False

    def parse_api_response(self, data):
        """解析API响应"""
        try:
            video_info = {}
            
            # 递归搜索视频信息
            found_info = self.extract_video_info_recursive(data)
            if found_info and len(found_info) >= 2:
                return found_info
            
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
        
        return None

    def extract_video_info_recursive(self, obj, depth=0, max_depth=15):
        """递归提取视频信息"""
        if depth > max_depth:
            return None
        
        video_info = {}
        
        if isinstance(obj, dict):
            # 查找关键字段
            field_mappings = {
                'title': ['desc', 'title', 'content', 'text', 'share_desc', 'aweme_desc'],
                'author': ['nickname', 'author', 'username', 'unique_id', 'sec_uid'],
                'cover': ['cover', 'thumbnail', 'poster', 'dynamic_cover', 'origin_cover'],
                'video_url': ['play_url', 'download_url', 'video_url', 'play_addr', 'play_url_list'],
                'video_id': ['aweme_id', 'item_id', 'video_id', 'id'],
                'duration': ['duration', 'video_duration'],
            }
            
            for info_key, possible_keys in field_mappings.items():
                for key in possible_keys:
                    if key in obj and obj[key]:
                        value = obj[key]
                        if info_key in ['video_url', 'cover']:
                            url = self.extract_url_from_value(value)
                            if url:
                                video_info[info_key] = url
                        else:
                            video_info[info_key] = str(value)
                        break
            
            # 如果找到了足够的信息，返回
            if len(video_info) >= 3:
                return video_info
            
            # 递归搜索
            for key, value in obj.items():
                if isinstance(value, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(value, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        elif isinstance(obj, list):
            for item in obj:
                if isinstance(item, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(item, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        return video_info if len(video_info) >= 2 else None

    def extract_url_from_value(self, value):
        """从值中提取URL"""
        if isinstance(value, str) and value.startswith(('http', '//')):
            return value
        elif isinstance(value, dict):
            url_keys = ['url', 'uri', 'src', 'href', 'play_url', 'download_url']
            for key in url_keys:
                if key in value:
                    url_value = value[key]
                    if isinstance(url_value, str) and url_value.startswith(('http', '//')):
                        return url_value
                    elif isinstance(url_value, list) and url_value:
                        return self.extract_url_from_value(url_value[0])
        elif isinstance(value, list) and value:
            return self.extract_url_from_value(value[0])
        
        return None

    def parse_video(self, url):
        """解析视频的主方法"""
        logger.info(f"🚀 开始解析抖音视频: {url}")
        
        # 1. 获取真实URL
        real_url = self.get_real_url(url)
        
        # 2. 提取视频ID
        video_id = self.extract_video_id(real_url)
        
        # 3. 尝试带签名的API请求
        logger.info("🔍 尝试带签名的API请求...")
        result = self.try_direct_api_with_signature(video_id)
        if result:
            return result
        
        # 4. 获取并解析页面内容
        logger.info("📄 获取并解析页面内容...")
        html_content = self.get_page_content(real_url)
        if html_content:
            result = self.parse_html_content(html_content)
            if result:
                return result
        
        logger.error("❌ 所有解析方法都失败了")
        return None

def main():
    """主函数"""
    test_url = "https://v.douyin.com/5UheTSjzZx0/"
    
    print("🎬 抖音视频解析器 - 最终版本")
    print("=" * 60)
    
    spider = DouyinFinalSpider()
    result = spider.parse_video(test_url)
    
    if result:
        print("\n🎉 解析成功！视频信息如下：")
        print("=" * 60)
        for key, value in result.items():
            if key == 'video_url':
                print(f"🎥 {key}: {value}")
            elif key == 'title':
                print(f"📝 {key}: {value}")
            elif key == 'author':
                print(f"👤 {key}: {value}")
            elif key == 'cover':
                print(f"🖼️  {key}: {value}")
            else:
                print(f"📋 {key}: {value}")
        print("=" * 60)
        
        if 'video_url' in result:
            print(f"\n✅ 视频直链获取成功！")
            print(f"🔗 下载链接: {result['video_url']}")
            print("💡 可以直接使用此链接下载视频")
    else:
        print("\n❌ 解析失败")
        print("💡 建议检查生成的调试文件获取更多信息")

if __name__ == "__main__":
    main()
