#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频解析器 - 终极版本
使用多种技术手段和逆向工程方法
"""

import requests
import re
import json
import time
import random
import hashlib
from urllib.parse import urlparse, parse_qs, unquote, quote
from fake_useragent import UserAgent
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DouyinUltimateSpider:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="139", "Google Chrome";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)

    def get_real_url(self, short_url):
        """获取短链接的真实URL"""
        try:
            response = self.session.head(short_url, allow_redirects=True, timeout=15)
            real_url = response.url
            logger.info(f"真实URL: {real_url}")
            return real_url
        except Exception as e:
            logger.error(f"获取真实URL失败: {e}")
            return short_url

    def extract_video_id(self, url):
        """从URL中提取视频ID"""
        patterns = [
            r'/video/(\d+)',
            r'modal_id=(\d+)',
            r'aweme_id=(\d+)',
            r'/share/video/(\d+)',
            r'/share/jx-video/(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                video_id = match.group(1)
                logger.info(f"提取到视频ID: {video_id}")
                return video_id
        
        logger.warning("未能提取到视频ID")
        return None

    def try_known_working_apis(self, video_id):
        """尝试已知有效的API端点"""
        if not video_id:
            return None
        
        # 已知的有效API端点（基于逆向工程）
        api_configs = [
            {
                'url': 'https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/',
                'params': {
                    'item_ids': video_id,
                    'dytk': '',
                },
                'headers': {
                    'Referer': 'https://www.douyin.com/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
            },
            {
                'url': f'https://www.douyin.com/aweme/v1/web/aweme/detail/',
                'params': {
                    'aweme_id': video_id,
                    'aid': '1128',
                    'app_name': 'douyin_web',
                    'device_platform': 'webapp',
                    'pc_client_type': '1',
                    'version_code': '170400',
                    'version_name': '17.4.0',
                    'channel': 'channel_pc_web',
                    'update_version_code': '170400',
                    'webid': '7' + str(random.randint(100000000000000000, 999999999999999999)),
                    'msToken': self.generate_ms_token(),
                },
                'headers': {
                    'Referer': 'https://www.douyin.com/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
            },
            # 尝试不同的移动端API
            {
                'url': 'https://aweme.snssdk.com/aweme/v1/aweme/detail/',
                'params': {
                    'aweme_id': video_id,
                    'aid': '1128',
                    'app_name': 'aweme',
                    'device_platform': 'android',
                    'version_code': '160',
                    'version_name': '16.0.0',
                    'channel': 'wandoujia',
                },
                'headers': {
                    'User-Agent': 'com.ss.android.ugc.aweme/160 (Linux; U; Android 9; zh_CN; MI 6; Build/PKQ1.190118.001; Cronet/TTNetVersion:b4d74d15 2020-04-23 QuicVersion:0144d358 2020-03-24)'
                }
            }
        ]
        
        for config in api_configs:
            try:
                logger.info(f"尝试API: {config['url']}")
                
                headers = self.headers.copy()
                headers.update(config.get('headers', {}))
                
                response = self.session.get(
                    config['url'], 
                    params=config.get('params', {}), 
                    headers=headers, 
                    timeout=15
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"API请求成功: {config['url']}")
                        
                        # 保存API响应
                        with open(f'douyin_working_api_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        # 检查响应是否有效
                        if self.is_valid_api_response(data):
                            result = self.parse_api_response(data)
                            if result:
                                return result
                        else:
                            logger.info(f"API响应无效或为空")
                    except json.JSONDecodeError:
                        logger.debug(f"API响应不是JSON格式")
                else:
                    logger.debug(f"API请求失败，状态码: {response.status_code}")
                    
            except Exception as e:
                logger.debug(f"API请求异常: {e}")
        
        return None

    def is_valid_api_response(self, data):
        """检查API响应是否有效"""
        if not isinstance(data, dict):
            return False
        
        # 检查常见的有效响应结构
        valid_indicators = [
            'aweme_list',
            'aweme_detail',
            'item_list',
            'aweme',
            'video',
            'play_url'
        ]
        
        for indicator in valid_indicators:
            if indicator in data:
                value = data[indicator]
                if isinstance(value, list) and len(value) > 0:
                    return True
                elif isinstance(value, dict) and value:
                    return True
        
        return False

    def generate_ms_token(self):
        """生成msToken"""
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
        return ''.join(random.choice(chars) for _ in range(107))

    def try_reverse_engineering_approach(self, video_id, real_url):
        """尝试逆向工程方法"""
        logger.info("🔧 尝试逆向工程方法...")
        
        # 方法1: 构造特殊的API请求
        result = self.try_special_api_construction(video_id)
        if result:
            return result
        
        # 方法2: 尝试解析页面中的隐藏数据
        result = self.try_hidden_data_extraction(real_url)
        if result:
            return result
        
        # 方法3: 尝试模拟真实用户行为
        result = self.try_user_behavior_simulation(real_url, video_id)
        if result:
            return result
        
        return None

    def try_special_api_construction(self, video_id):
        """尝试特殊的API构造"""
        if not video_id:
            return None
        
        # 基于逆向工程的特殊API端点
        special_apis = [
            f"https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids={video_id}&dytk=",
            f"https://www.douyin.com/aweme/v1/web/aweme/detail/?aweme_id={video_id}&aid=6383&app_name=douyin_web&device_platform=webapp&pc_client_type=1&version_code=170400",
            f"https://m.douyin.com/web/api/v2/aweme/iteminfo/?item_ids={video_id}",
        ]
        
        for api_url in special_apis:
            try:
                # 使用特殊的请求头
                special_headers = {
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                    'Referer': 'https://www.douyin.com/',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'X-Requested-With': 'XMLHttpRequest',
                }
                
                response = self.session.get(api_url, headers=special_headers, timeout=15)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"特殊API请求成功: {api_url}")
                        
                        # 保存响应
                        with open(f'douyin_special_api_{int(time.time())}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        if self.is_valid_api_response(data):
                            result = self.parse_api_response(data)
                            if result:
                                return result
                    except json.JSONDecodeError:
                        pass
                        
            except Exception as e:
                logger.debug(f"特殊API请求失败: {e}")
        
        return None

    def try_hidden_data_extraction(self, real_url):
        """尝试提取页面中的隐藏数据"""
        try:
            # 使用特殊的请求头获取页面
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Referer': 'https://www.douyin.com/',
            }
            
            response = self.session.get(real_url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                html = response.text
                logger.info(f"获取页面成功，长度: {len(html)}")
                
                # 保存HTML
                with open(f'douyin_hidden_data_{int(time.time())}.html', 'w', encoding='utf-8') as f:
                    f.write(html)
                
                # 尝试提取视频信息
                return self.extract_video_info_from_html(html)
                
        except Exception as e:
            logger.error(f"隐藏数据提取失败: {e}")
        
        return None

    def extract_video_info_from_html(self, html):
        """从HTML中提取视频信息"""
        try:
            # 查找可能包含视频信息的模式
            patterns = [
                r'"desc"\s*:\s*"([^"]+)"',
                r'"nickname"\s*:\s*"([^"]+)"',
                r'"play_url"\s*:\s*"([^"]+)"',
                r'"download_url"\s*:\s*"([^"]+)"',
                r'"cover"\s*:\s*"([^"]+)"',
                r'"aweme_id"\s*:\s*"([^"]+)"',
            ]
            
            video_info = {}
            
            for pattern in patterns:
                matches = re.findall(pattern, html)
                if matches:
                    if 'desc' in pattern:
                        video_info['title'] = matches[0]
                    elif 'nickname' in pattern:
                        video_info['author'] = matches[0]
                    elif 'play_url' in pattern or 'download_url' in pattern:
                        video_info['video_url'] = matches[0]
                    elif 'cover' in pattern:
                        video_info['cover'] = matches[0]
                    elif 'aweme_id' in pattern:
                        video_info['video_id'] = matches[0]
            
            if len(video_info) >= 2:
                logger.info(f"从HTML提取到视频信息: {video_info}")
                return video_info
                
        except Exception as e:
            logger.error(f"HTML视频信息提取失败: {e}")
        
        return None

    def try_user_behavior_simulation(self, real_url, video_id):
        """尝试模拟用户行为"""
        try:
            # 模拟用户访问流程
            logger.info("模拟用户访问流程...")
            
            # 1. 先访问首页
            self.session.get('https://www.douyin.com/', timeout=10)
            time.sleep(random.uniform(1, 3))
            
            # 2. 再访问视频页面
            response = self.session.get(real_url, timeout=15)
            
            if response.status_code == 200:
                html = response.text
                
                # 3. 尝试找到AJAX请求的端点
                ajax_patterns = [
                    r'/aweme/v\d+/[^"\']+',
                    r'/web/api/v\d+/[^"\']+',
                    r'https://[^"\']*douyin[^"\']*api[^"\']*',
                ]
                
                for pattern in ajax_patterns:
                    matches = re.findall(pattern, html)
                    for match in matches:
                        if video_id in match or 'aweme' in match:
                            logger.info(f"发现可能的AJAX端点: {match}")
                            
                            # 尝试请求这个端点
                            if not match.startswith('http'):
                                match = 'https://www.douyin.com' + match
                            
                            try:
                                ajax_response = self.session.get(match, timeout=10)
                                if ajax_response.status_code == 200:
                                    data = ajax_response.json()
                                    if self.is_valid_api_response(data):
                                        result = self.parse_api_response(data)
                                        if result:
                                            return result
                            except:
                                continue
                
        except Exception as e:
            logger.error(f"用户行为模拟失败: {e}")
        
        return None

    def parse_api_response(self, data):
        """解析API响应"""
        try:
            video_info = self.extract_video_info_recursive(data)
            if video_info and len(video_info) >= 2:
                return video_info
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
        return None

    def extract_video_info_recursive(self, obj, depth=0, max_depth=15):
        """递归提取视频信息"""
        if depth > max_depth:
            return None
        
        video_info = {}
        
        if isinstance(obj, dict):
            # 查找关键字段
            field_mappings = {
                'title': ['desc', 'title', 'content', 'text', 'share_desc', 'aweme_desc'],
                'author': ['nickname', 'author', 'username', 'unique_id', 'sec_uid'],
                'cover': ['cover', 'thumbnail', 'poster', 'dynamic_cover', 'origin_cover'],
                'video_url': ['play_url', 'download_url', 'video_url', 'play_addr', 'play_url_list'],
                'video_id': ['aweme_id', 'item_id', 'video_id', 'id'],
                'duration': ['duration', 'video_duration'],
            }
            
            for info_key, possible_keys in field_mappings.items():
                for key in possible_keys:
                    if key in obj and obj[key]:
                        value = obj[key]
                        if info_key in ['video_url', 'cover']:
                            url = self.extract_url_from_value(value)
                            if url:
                                video_info[info_key] = url
                        else:
                            video_info[info_key] = str(value)
                        break
            
            # 如果找到了足够的信息，返回
            if len(video_info) >= 3:
                return video_info
            
            # 递归搜索
            for key, value in obj.items():
                if isinstance(value, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(value, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        elif isinstance(obj, list):
            for item in obj:
                if isinstance(item, (dict, list)) and depth < max_depth:
                    result = self.extract_video_info_recursive(item, depth + 1, max_depth)
                    if result and len(result) >= 3:
                        return result
        
        return video_info if len(video_info) >= 2 else None

    def extract_url_from_value(self, value):
        """从值中提取URL"""
        if isinstance(value, str) and value.startswith(('http', '//')):
            return value
        elif isinstance(value, dict):
            url_keys = ['url', 'uri', 'src', 'href', 'play_url', 'download_url']
            for key in url_keys:
                if key in value:
                    url_value = value[key]
                    if isinstance(url_value, str) and url_value.startswith(('http', '//')):
                        return url_value
                    elif isinstance(url_value, list) and url_value:
                        return self.extract_url_from_value(url_value[0])
        elif isinstance(value, list) and value:
            return self.extract_url_from_value(value[0])
        
        return None

    def parse_video(self, url):
        """解析视频的主方法"""
        logger.info(f"🚀 开始解析抖音视频: {url}")
        
        # 1. 获取真实URL
        real_url = self.get_real_url(url)
        
        # 2. 提取视频ID
        video_id = self.extract_video_id(real_url)
        
        # 3. 尝试已知有效的API
        logger.info("🔍 尝试已知有效的API...")
        result = self.try_known_working_apis(video_id)
        if result:
            return result
        
        # 4. 尝试逆向工程方法
        logger.info("🔧 尝试逆向工程方法...")
        result = self.try_reverse_engineering_approach(video_id, real_url)
        if result:
            return result
        
        logger.error("❌ 所有解析方法都失败了")
        return None

def main():
    """主函数"""
    test_url = "https://v.douyin.com/5UheTSjzZx0/"
    
    print("🎬 抖音视频解析器 - 终极版本")
    print("=" * 60)
    
    spider = DouyinUltimateSpider()
    result = spider.parse_video(test_url)
    
    if result:
        print("\n🎉 解析成功！视频信息如下：")
        print("=" * 60)
        for key, value in result.items():
            if key == 'video_url':
                print(f"🎥 视频链接: {value}")
            elif key == 'title':
                print(f"📝 标题: {value}")
            elif key == 'author':
                print(f"👤 作者: {value}")
            elif key == 'cover':
                print(f"🖼️  封面: {value}")
            else:
                print(f"📋 {key}: {value}")
        print("=" * 60)
        
        if 'video_url' in result:
            print(f"\n✅ 视频直链获取成功！")
            print(f"🔗 下载链接: {result['video_url']}")
            print("💡 可以直接使用此链接下载视频")
    else:
        print("\n❌ 解析失败")
        print("💡 抖音的反爬虫机制非常严格，可能需要更高级的技术手段")
        print("💡 建议：")
        print("   1. 使用专业的抖音API服务")
        print("   2. 使用浏览器自动化工具（如Selenium）")
        print("   3. 使用代理IP和更复杂的反检测技术")

if __name__ == "__main__":
    main()
